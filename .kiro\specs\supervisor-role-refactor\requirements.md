# Requirements Document

## Introduction

The DERS system currently uses "supervisor" as a role in the users table, similar to admin, user, and guest roles. This requirement aims to refactor the supervisor functionality to be a capability (like M&E) rather than a role, using an `is_supervisor` field instead of checking the role field. This change will allow users to have supervisor capabilities while maintaining their primary role (admin, user, guest).

## Requirements

### Requirement 1

**User Story:** As a system administrator, I want to manage supervisor capabilities independently from user roles, so that users can have supervisor privileges while maintaining their primary role designation.

#### Acceptance Criteria

1. WHEN the system is updated THEN the users table SHALL include an `is_supervisor` boolean field
2. WHEN a user is assigned supervisor capability THEN the `is_supervisor` field SHALL be set to 1 (true)
3. WHEN a user is not a supervisor THEN the `is_supervisor` field SHALL be set to 0 (false) by default
4. WHEN the database is migrated THEN existing users with role='supervisor' SHALL have `is_supervisor` set to 1 and their role changed to 'user'

### Requirement 2

**User Story:** As a developer, I want all code that checks for supervisor role to use the new `is_supervisor` field, so that the system correctly identifies supervisors based on capability rather than role.

#### Acceptance Criteria

1. WHEN code needs to identify supervisors THEN it SHALL check the `is_supervisor` field instead of role='supervisor'
2. WHEN the UsersModel is updated THEN it SHALL include methods to get users by supervisor capability
3. WHEN validation rules are updated THEN they SHALL no longer include 'supervisor' as a valid role option
4. WHEN authentication checks are performed THEN they SHALL use the `is_supervisor` field for supervisor-specific access control

### Requirement 3

**User Story:** As a system user, I want the user interface to reflect the new supervisor capability system, so that supervisor status is displayed and managed correctly.

#### Acceptance Criteria

1. WHEN viewing user lists THEN supervisor capability SHALL be displayed separately from role
2. WHEN editing users THEN there SHALL be a checkbox or toggle for supervisor capability
3. WHEN creating users THEN there SHALL be an option to assign supervisor capability
4. WHEN displaying user information THEN both role and supervisor status SHALL be clearly indicated

### Requirement 4

**User Story:** As a system administrator, I want the role validation to be updated to remove supervisor as a valid role, so that the system enforces the new capability-based approach.

#### Acceptance Criteria

1. WHEN validating user roles THEN 'supervisor' SHALL NOT be accepted as a valid role
2. WHEN creating or updating users THEN the valid roles SHALL be limited to 'admin', 'user', and 'guest'
3. WHEN form validation occurs THEN error messages SHALL reflect the updated role options
4. WHEN database constraints are applied THEN the role enum SHALL exclude 'supervisor'

### Requirement 5

**User Story:** As a developer, I want all authentication and authorization logic to be updated, so that supervisor capabilities work correctly with the new field.

#### Acceptance Criteria

1. WHEN checking supervisor access THEN the system SHALL use `is_supervisor` field instead of role comparison
2. WHEN session data is set THEN it SHALL include supervisor capability information
3. WHEN authorization filters are applied THEN they SHALL check the `is_supervisor` field for supervisor-specific permissions
4. WHEN user statistics are generated THEN they SHALL include supervisor capability counts separately from role counts
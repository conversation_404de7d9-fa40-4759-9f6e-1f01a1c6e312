<?php
/**
 * Position-specific Analytics View
 * Shows detailed geographical breakdown for a specific position
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url() ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('analytics/positions') ?>">Analytics</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Position Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-1">
                                <i class="fas fa-briefcase me-2"></i>
                                <?= esc($position['designation']) ?>
                            </h3>
                            <p class="mb-0">
                                <span class="badge bg-light text-dark me-2"><?= esc($position['position_reference']) ?></span>
                                <span class="badge bg-light text-dark me-2"><?= esc($position['classification']) ?></span>
                                <span class="badge bg-light text-dark"><?= esc($position['group_name'] ?? 'No Group') ?></span>
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <h2 class="mb-0">
                                <i class="fas fa-eye me-2"></i>
                                <?= number_format($position['view_count'] ?? 0) ?>
                            </h2>
                            <p class="mb-0">Total Views</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($gpsStats) ?></h4>
                            <p class="mb-0">GPS Locations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-crosshairs fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= number_format($uniqueLocations) ?></h4>
                            <p class="mb-0">Unique Locations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($coordinates) ?></h4>
                            <p class="mb-0">Mapped Points</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= array_sum(array_column($gpsStats, 'unique_visitors')) ?></h4>
                            <p class="mb-0">Unique Visitors</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- GPS Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-crosshairs me-2"></i>
                        Views by GPS Location
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($gpsStats)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-crosshairs fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No GPS data available</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>GPS Coordinates</th>
                                        <th>Views</th>
                                        <th>Unique Visitors</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $totalGpsViews = array_sum(array_column($gpsStats, 'view_count'));
                                    foreach (array_slice($gpsStats, 0, 15) as $gps):
                                        $percentage = $totalGpsViews > 0 ? ($gps['view_count'] / $totalGpsViews) * 100 : 0;
                                    ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-map-pin me-1"></i>
                                                <code><?= esc($gps['gps_coordinates']) ?></code>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= number_format($gps['view_count']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($gps['unique_visitors']) ?></span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 15px; width: 100px;">
                                                    <div class="progress-bar progress-bar-striped" role="progressbar"
                                                         style="width: <?= $percentage ?>%"
                                                         aria-valuenow="<?= $percentage ?>"
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        <?= number_format($percentage, 1) ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (count($gpsStats) > 15): ?>
                            <div class="text-center mt-3">
                                <small class="text-muted">Showing top 15 locations out of <?= count($gpsStats) ?> total</small>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Views Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Daily Views (Last 30 Days)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyViewsChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Mapping Coordinates -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map me-2"></i>
                        Mapping Coordinates
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($coordinates)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-map fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No coordinate data available for mapping</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>GPS Coordinates</th>
                                        <th>Latitude</th>
                                        <th>Longitude</th>
                                        <th>Views</th>
                                        <th>Unique Visitors</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($coordinates as $coord): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-crosshairs me-1"></i>
                                                <code><?= esc($coord['gps_coordinates']) ?></code>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= $coord['latitude'] ? number_format($coord['latitude'], 6) : 'N/A' ?>
                                                </small>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= $coord['longitude'] ? number_format($coord['longitude'], 6) : 'N/A' ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= number_format($coord['view_count']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($coord['unique_visitors']) ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // Daily Views Chart
    const dailyData = <?= json_encode($dailyStats) ?>;
    const ctx = document.getElementById('dailyViewsChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dailyData.map(item => item.view_date),
            datasets: [{
                label: 'Daily Views',
                data: dailyData.map(item => item.view_count),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>

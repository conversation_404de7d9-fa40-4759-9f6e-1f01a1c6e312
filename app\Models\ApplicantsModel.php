<?php namespace App\Models;

use CodeIgniter\Model;

class ApplicantsModel extends Model
{
    protected $table = 'applicants';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';

    protected $allowedFields = [
        'unique_id',
        'email',
        'password',
        'first_name',
        'last_name',
        'gender',
        'dobirth',
        'place_of_origin',
        'id_photo_path',
        'contact_details',
        'location_address',
        'id_numbers',
        'current_employer',
        'current_position',
        'current_salary',
        'citizenship',
        'marital_status',
        'date_of_marriage',
        'spouse_employer',
        'is_public_servant',
        'public_service_file_number',
        'employee_of_org_id',
        'children',
        'offence_convicted',
        'referees',
        'how_did_you_hear_about_us',
        'signature_path',
        'publications',
        'awards',
        'status',
        'activation_token',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Callbacks
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    protected function hashPassword(array $data)
    {
        if (!isset($data['data']['password'])) {
            return $data;
        }

        $data['data']['password'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
        return $data;
    }

    /**
     * Get applicant by email
     *
     * @param string $email
     * @return array|null
     */
    public function getApplicantByEmail($email)
    {
        return $this->where('email', $email)->first();
    }

    /**
     * Get applicant by unique ID
     *
     * @param string $uniqueId
     * @return array|null
     */
    public function getApplicantByUniqueId($uniqueId)
    {
        return $this->where('unique_id', $uniqueId)->first();
    }

    /**
     * Get active applicants
     *
     * @return array
     */
    public function getActiveApplicants()
    {
        return $this->where('status', 1)->findAll();
    }

    /**
     * Verify applicant password
     *
     * @param string $email
     * @param string $password
     * @return array|null
     */
    public function verifyApplicant($email, $password)
    {
        $applicant = $this->getApplicantByEmail($email);
        
        if ($applicant && password_verify($password, $applicant['password'])) {
            return $applicant;
        }
        
        return null;
    }

    /**
     * Activate applicant account
     *
     * @param string $token
     * @return bool
     */
    public function activateAccount($token)
    {
        $applicant = $this->where('activation_token', $token)->first();
        
        if ($applicant) {
            return $this->update($applicant['id'], [
                'status' => 1,
                'activation_token' => null
            ]);
        }
        
        return false;
    }

    /**
     * Get applicant with organization details
     *
     * @param int $id
     * @return array|null
     */
    public function getApplicantWithOrganization($id)
    {
        return $this->select('applicants.*, dakoii_org.org_name')
                    ->join('dakoii_org', 'dakoii_org.id = applicants.employee_of_org_id', 'left')
                    ->where('applicants.id', $id)
                    ->first();
    }

    /**
     * Get all active organizations for dropdown
     *
     * @return array
     */
    public function getOrganizationsForDropdown()
    {
        $orgModel = new \App\Models\DakoiiOrgModel();
        $organizations = $orgModel->select('id, org_name')
                                  ->where('is_active', 1)
                                  ->orderBy('org_name', 'ASC')
                                  ->findAll();

        $dropdown = ['' => 'Not Listed'];
        foreach ($organizations as $org) {
            $dropdown[$org['id']] = $org['org_name'];
        }

        return $dropdown;
    }
}

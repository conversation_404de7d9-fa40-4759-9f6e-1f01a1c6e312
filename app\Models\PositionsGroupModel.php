<?php

namespace App\Models;

use CodeIgniter\Model;

class PositionsGroupModel extends Model
{
    protected $table         = 'positions_groups';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_id',
        'parent_id',
        'group_name',
        'description',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Set the return type to array
    protected $returnType    = 'array';

    // Validation rules
    protected $validationRules = [
        'org_id' => 'required|numeric',
        'exercise_id' => 'required|numeric',
        'parent_id' => 'permit_empty|numeric',
        'group_name' => 'required|max_length[255]',
        'description' => 'permit_empty|max_length[500]'
    ];

    // Validation messages
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric' => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric' => 'Exercise ID must be a number'
        ],
        'parent_id' => [
            'numeric' => 'Parent ID must be a number'
        ],
        'group_name' => [
            'required' => 'Group name is required',
            'max_length' => 'Group name cannot exceed 255 characters'
        ],
        'description' => [
            'max_length' => 'Description cannot exceed 500 characters'
        ]
    ];

    // Skip validation for these fields during updates
    protected $validationSkipOnUpdate = ['org_id', 'exercise_id'];

    /**
     * Check if group name is unique within the same exercise
     */
    public function isGroupNameUniqueInExercise($groupName, $exerciseId, $excludeId = null)
    {
        $query = $this->where('exercise_id', $exerciseId)
                      ->where('group_name', $groupName);

        // Exclude current record if updating
        if ($excludeId) {
            $query->where('id !=', $excludeId);
        }

        $existingGroup = $query->first();
        return $existingGroup === null;
    }

    /**
     * Get position groups by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getPositionGroupsByExerciseId($exerciseId)
    {
        return $this->select('
                positions_groups.*,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions_groups.org_id = dakoii_org.id', 'left')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->orderBy('positions_groups.group_name', 'ASC')
            ->findAll();
    }

    /**
     * Get position groups with position count by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getPositionGroupsWithCountByExerciseId($exerciseId)
    {
        return $this->select('
                positions_groups.*,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name,
                COUNT(positions.id) as position_count
            ')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions_groups.org_id = dakoii_org.id', 'left')
            ->join('positions', 'positions_groups.id = positions.position_group_id', 'left')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->groupBy('positions_groups.id')
            ->orderBy('positions_groups.group_name', 'ASC')
            ->findAll();
    }
}
# DERS (Dakoii Echad Recruitment & Selection System) - Complete Workflow Guide

## System Overview

DERS is Papua New Guinea's first AI-integrated Recruitment and Selection system, designed to streamline the entire recruitment process from job advertising to final selection. The system serves multiple government organizations with role-based access control and comprehensive applicant management.

## System Architecture

### Multi-Tenant Structure
- **Organizations**: Multiple government organizations can use the system independently
- **Role-Based Access**: System Admin (Dakoii), Organization Admin, HR Supervisor, HR User, Guest
- **Data Segregation**: Secure separation of organizational data
- **Geographic Integration**: PNG provinces, districts, and LLG data integration

### Core Database Tables
- **exercises**: Job advertisement campaigns
- **positions**: Individual job positions within exercises
- **positions_groups**: Grouping of related positions
- **applicants**: Applicant registration data
- **appx_application_details**: Complete application submissions
- **users**: System users with organizational access
- **dakoii_org**: Organization management

## Complete Workflow: From Advertising to Final Selection

### Stage 1: Exercise Creation and Setup

**1.1 Exercise Planning**
- HR staff creates a new recruitment exercise in the system
- Exercise contains: name, gazette number, advertisement details, publication dates
- Status starts as "draft" for internal preparation
- Pre-screening criteria and applicant information requirements are defined

**1.2 Position Definition**
- Individual positions are created within the exercise
- Each position includes: reference code, designation, classification, location, salary
- Job descriptions (JD) are uploaded as PDF files with AI text extraction
- Qualifications, knowledge requirements, skills, and experience criteria are specified
- Positions are grouped logically within the exercise

**1.3 Exercise Status Management**
Exercise progresses through defined statuses:
- **Draft**: Work in progress, not visible to public
- **Published**: Publicly visible and accepting applications
- **Selection**: Applications closed, selection process active
- **Archived**: Process completed and archived

### Stage 2: Public Advertisement and Application Submission

**2.1 Public Visibility**
- Published exercises appear on the public jobs portal
- Positions display organization details, requirements, and application deadlines
- Internal vs external positions are clearly marked
- Random ordering with lazy loading pagination for fair exposure

**2.2 Applicant Registration**
- Applicants create accounts with email verification
- Comprehensive profile creation including:
  - Personal details (name, gender, DOB, origin, citizenship)
  - Contact information and address
  - Current employment details
  - Public servant status (if applicable)
  - Educational background
  - Work experience history
  - Professional references

**2.3 Application Submission Process**
- Applicants browse available positions and select desired roles
- System prevents duplicate applications for the same position
- Application form captures:
  - Position-specific information
  - Supporting documents (CV, certificates, references)
  - Additional qualifications and experience details
- File uploads support PDF format with AI text extraction
- Application receives unique reference number
- Email confirmation sent to applicant with receipt details

### Stage 3: Application Processing and Management

**3.1 Incoming Applications**
- HR staff monitor incoming applications in real-time
- Applications are automatically linked to exercises and positions
- System tracks application completeness and submission timestamps
- Bulk application management tools available

**3.2 Document Processing**
- AI-powered text extraction from uploaded PDFs
- Support for complex documents including images, tables, and charts
- Large documents automatically split for processing efficiency
- Extracted text stored for searchability and analysis

**3.3 Application Validation**
- Server-side validation ensures data integrity
- Exercise and position linkage verification
- Duplicate application prevention
- Required field validation and completeness checks

### Stage 4: Pre-Screening and Evaluation

**4.1 Pre-Screening Interface**
- Dedicated interface for HR staff to review applications
- Two-column layout: applicant details (left) and criteria assessment (right)
- Tabbed navigation for different applicant information sections
- Progressive PDF page loading for efficient document review

**4.2 AI-Assisted Analysis**
- Automatic AI analysis of applicant documents
- Comparison against position requirements
- Scoring suggestions based on qualifications match
- AI analysis results stored in session data for review

**4.3 Manual Assessment**
- HR staff review AI recommendations and make final decisions
- Pre-screening data stored in JSON format including:
  - Assessment remarks
  - AI analysis results
  - Status decisions
  - Timestamp and reviewer information

### Stage 5: Rating and Scoring System

**5.1 Comprehensive Rating Criteria**
- **Age and Demographics**: Demographic factor considerations
- **Educational Qualifications**: 1-10 scale rating system
- **Work Experience**: Separate scoring for public/private and relevant/non-relevant experience
- **Skills and Competencies**: Technical and soft skills assessment
- **Knowledge Assessment**: Domain-specific knowledge evaluation
- **Public Service Experience**: Understanding of public service values
- **Leadership Capabilities**: Management and supervisory experience
- **Overall Assessment**: Holistic candidate evaluation

**5.2 AI-Assisted Rating**
- Optional AI analysis for objective scoring
- Pattern matching for experience levels and qualifications
- Automated score suggestions based on document analysis
- Human verification and adjustment capabilities

**5.3 Manual Review Process**
- HR staff can override AI suggestions
- Detailed scoring rationale documentation
- Multi-criteria decision matrix
- Ranking generation based on total scores

### Stage 6: Shortlisting Process

**6.1 Candidate Ranking**
- Automatic ranking based on total assessment scores
- Configurable shortlisting criteria and thresholds
- Position-specific shortlist generation
- Elimination tracking with detailed remarks

**6.2 Shortlist Management**
- Dedicated shortlisting interface for HR staff
- Application status updates: shortlisted, eliminated, withdrawn
- Bulk status update capabilities
- Shortlisting decision audit trail

**6.3 Candidate Notification**
- Automated email notifications to shortlisted candidates
- Interview scheduling coordination
- Professional communication templates
- Status update tracking

### Stage 7: Interview Management

**7.1 Interview Session Setup**
- Creation of interview sessions for shortlisted candidates
- Interview panel composition and scheduling
- Interviewer assignment and management
- Session logistics coordination

**7.2 Interview Process**
- Structured interview frameworks
- Scoring mechanisms for interview performance
- Panel member input collection
- Interview outcome documentation

**7.3 Final Assessment Integration**
- Combination of pre-screening, rating, and interview scores
- Weighted scoring methodologies
- Final ranking determination
- Selection recommendation generation

### Stage 8: Final Selection and Reporting

**8.1 Selection Decision**
- Final candidate selection based on comprehensive assessment
- Selection committee review and approval
- Decision documentation and rationale
- Unsuccessful candidate feedback preparation

**8.2 Comprehensive Reporting**
- Exercise-level reporting and analytics
- Application statistics and demographics
- Selection process audit trails
- Performance metrics and insights

**8.3 Process Completion**
- Final notifications to all applicants
- Successful candidate onboarding coordination
- Exercise archival and documentation
- Lessons learned and process improvement

## Key System Features

### AI Integration
- **Gemini AI Integration**: Advanced text extraction and analysis
- **Multimodal Processing**: Handles text, images, tables, and charts
- **Intelligent Scoring**: AI-assisted candidate evaluation
- **Document Analysis**: Automated qualification matching

### Security and Compliance
- **Role-Based Access Control**: Granular permission management
- **Data Segregation**: Organization-specific data isolation
- **Audit Trails**: Comprehensive activity logging
- **Secure File Handling**: Protected document storage and access

### User Experience
- **Responsive Design**: Mobile-friendly interface
- **Progressive Web App**: Installable on mobile devices
- **Real-time Updates**: Live application status tracking
- **Intuitive Navigation**: User-friendly interface design

### Technical Infrastructure
- **CodeIgniter 4 Framework**: Robust PHP framework
- **MySQL Database**: Reliable data storage
- **XAMPP Development**: Local development environment
- **RESTful Architecture**: Standard API design patterns

## Contact and Support

- **General Business Enquiries**: <EMAIL>
- **Technical Support**: <EMAIL>
- **Website**: www.dakoiims.com

---

*This document provides a comprehensive overview of the DERS system workflow. For technical implementation details, refer to the system documentation and codebase.*

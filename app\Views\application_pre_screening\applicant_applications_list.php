<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening') ?>">Pre-Screening</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>">Pre-Screening Applicants</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Applicant Applications</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applicants
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Applicant & Exercise Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-body">
                <h5 class="card-title text-red">Applicant & Exercise Information</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Applicant Name:</strong> <?= esc($applicant_name) ?></p>
                        <p><strong>Applicant ID:</strong> <span class="badge bg-primary"><?= esc($applicant_id) ?></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Exercise:</strong> <?= esc($exercise['exercise_name']) ?></p>
                        <p><strong>Total Applications:</strong> <span class="badge bg-success"><?= count($applications) ?></span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Applications List -->
<div class="row">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt"></i> Applications by <?= esc($applicant_name) ?>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($applications)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-4x text-muted mb-4"></i>
                        <h5 class="text-muted">No Applications Found</h5>
                        <p class="text-muted">This applicant has no applications in this exercise.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="applicationsTable">
                            <thead class="table-dark">
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Application No.</th>
                                        <th scope="col">Position</th>
                                        <th scope="col">Position Group</th>
                                        <th scope="col">Classification</th>
                                        <th scope="col">Location</th>
                                        <th scope="col">Application Status</th>
                                        <th scope="col">Pre-Screen Status</th>
                                        <th scope="col">Applied Date</th>
                                        <th scope="col">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applications as $index => $application): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <span class="badge bg-info"><?= esc($application['application_number']) ?></span>
                                            </td>
                                            <td>
                                                <strong class="text-red"><?= esc($application['position_title']) ?></strong><br>
                                                <small class="text-muted"><?= esc($application['position_reference']) ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?= esc($application['group_name']) ?></span>
                                            </td>
                                            <td><?= esc($application['classification']) ?></td>
                                            <td><?= esc($application['position_location']) ?></td>
                                            <td>
                                                <?php
                                                $statusClass = 'secondary';
                                                $statusText = $application['application_status'] ?? 'Pending';
                                                switch (strtolower($statusText)) {
                                                    case 'approved':
                                                        $statusClass = 'success';
                                                        break;
                                                    case 'rejected':
                                                        $statusClass = 'danger';
                                                        break;
                                                    case 'pending':
                                                        $statusClass = 'warning';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge bg-<?= $statusClass ?>"><?= esc($statusText) ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                $preScreenStatus = $application['pre_screened_status'] ?? 'Not Screened';
                                                $preScreenClass = 'secondary';
                                                switch (strtolower($preScreenStatus)) {
                                                    case 'passed':
                                                        $preScreenClass = 'success';
                                                        break;
                                                    case 'failed':
                                                        $preScreenClass = 'danger';
                                                        break;
                                                    case 'not screened':
                                                        $preScreenClass = 'warning';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge bg-<?= $preScreenClass ?>"><?= esc($preScreenStatus) ?></span>
                                            </td>
                                            <td>
                                                <?= date('M d, Y', strtotime($application['created_at'])) ?><br>
                                                <small class="text-muted"><?= date('H:i', strtotime($application['created_at'])) ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('application_pre_screening/show/' . $application['id']) ?>" class="btn btn-sm btn-outline-primary" title="View Application">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('acknowledged_applications/view/' . $application['id']) ?>" class="btn btn-sm btn-outline-info" title="View Full Details">
                                                        <i class="fas fa-file-alt"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                    <!-- Summary Information -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info border-0">
                                <i class="fas fa-info-circle text-red"></i>
                                <strong>Summary:</strong>
                                <?= esc($applicant_name) ?> has submitted <?= count($applications) ?> application(s) for different positions in this exercise.
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        "pageLength": 25,
        "order": [[ 1, "asc" ]], // Sort by Application Number
        "columnDefs": [
            { "orderable": false, "targets": [9] } // Disable sorting on Actions column
        ],
        "language": {
            "search": "Search Applications:",
            "lengthMenu": "Show _MENU_ applications per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ applications",
            "infoEmpty": "No applications found",
            "infoFiltered": "(filtered from _MAX_ total applications)"
        }
    });
});
</script>
<?= $this->endSection() ?>

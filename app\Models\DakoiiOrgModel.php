<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiOrgModel extends Model
{
    protected $table      = 'dakoii_org';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'org_code',
        'org_name',
        'description',
        'location_lock_province',
        'location_lock_country',
        'logo_path',
        'is_locationlocked',
        'postal_address',
        'phone_numbers',
        'email_addresses',
        'is_active',
        'license_status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules = [
        'org_code' => [
            'label' => 'Organization Code',
            'rules' => 'required|max_length[100]|is_unique[dakoii_org.org_code,id,{id}]'
        ],
        'org_name' => [
            'label' => 'Organization Name',
            'rules' => 'required|max_length[255]'
        ],
        'description' => [
            'label' => 'Description',
            'rules' => 'permit_empty'
        ],
        'location_lock_province' => [
            'label' => 'Location Lock Province',
            'rules' => 'permit_empty|max_length[100]'
        ],
        'location_lock_country' => [
            'label' => 'Location Lock Country',
            'rules' => 'permit_empty|max_length[100]'
        ],
        'logo_path' => [
            'label' => 'Logo Path',
            'rules' => 'permit_empty|max_length[255]'
        ],
        'is_locationlocked' => [
            'label' => 'Is Location Locked',
            'rules' => 'permit_empty|in_list[0,1]'
        ],
        'postal_address' => [
            'label' => 'Postal Address',
            'rules' => 'permit_empty'
        ],
        'phone_numbers' => [
            'label' => 'Phone Numbers',
            'rules' => 'permit_empty'
        ],
        'email_addresses' => [
            'label' => 'Email Addresses',
            'rules' => 'permit_empty|valid_email'
        ],
        'is_active' => [
            'label' => 'Is Active',
            'rules' => 'permit_empty|in_list[0,1]'
        ],
        'license_status' => [
            'label' => 'License Status',
            'rules' => 'permit_empty|max_length[50]'
        ],
        'created_by' => [
            'label' => 'Created By',
            'rules' => 'permit_empty|integer'
        ],
        'updated_by' => [
            'label' => 'Updated By',
            'rules' => 'permit_empty|integer'
        ],
        'deleted_by' => [
            'label' => 'Deleted By',
            'rules' => 'permit_empty|integer'
        ]
    ];

    protected $validationMessages = [
        'org_code' => [
            'required' => 'Organization code is required',
            'max_length' => 'Organization code cannot exceed 100 characters',
            'is_unique' => 'Organization code already exists'
        ],
        'org_name' => [
            'required' => 'Organization name is required',
            'max_length' => 'Organization name cannot exceed 255 characters'
        ],
        'location_lock_province' => [
            'required' => 'Location lock province is required',
            'max_length' => 'Location lock province cannot exceed 100 characters'
        ],
        'location_lock_country' => [
            'required' => 'Location lock country is required',
            'max_length' => 'Location lock country cannot exceed 100 characters'
        ],
        'logo_path' => [
            'required' => 'Logo path is required',
            'max_length' => 'Logo path cannot exceed 255 characters'
        ],
        'postal_address' => [
            'required' => 'Postal address is required'
        ],
        'phone_numbers' => [
            'required' => 'Phone numbers are required'
        ],
        'email_addresses' => [
            'required' => 'Email addresses are required',
            'valid_emails' => 'Please provide valid email addresses'
        ]
    ];

    protected $skipValidation = false;

    // Database field types for reference
    protected $fieldTypes = [
        'id' => 'int(11) UNSIGNED AUTO_INCREMENT',
        'org_code' => 'varchar(100)',
        'org_name' => 'varchar(255)',
        'description' => 'text',
        'location_lock_province' => 'varchar(100)',
        'location_lock_country' => 'varchar(100)',
        'logo_path' => 'varchar(255)',
        'is_locationlocked' => 'tinyint(1)',
        'postal_address' => 'text',
        'phone_numbers' => 'text',
        'email_addresses' => 'text',
        'is_active' => 'tinyint(1)',
        'license_status' => 'varchar(50)',
        'created_by' => 'int(11) UNSIGNED',
        'updated_by' => 'int(11) UNSIGNED',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'deleted_by' => 'int(11) UNSIGNED'
    ];

    /**
     * Get active organizations
     *
     * @return array
     */
    public function getActiveOrganizations()
    {
        return $this->where('is_active', 1)->findAll();
    }

    /**
     * Get organization by code
     *
     * @param string $orgCode
     * @return array|null
     */
    public function getByOrgCode(string $orgCode)
    {
        return $this->where('org_code', $orgCode)->first();
    }

    /**
     * Get organizations by province
     *
     * @param string $province
     * @return array
     */
    public function getByProvince(string $province)
    {
        return $this->where('location_lock_province', $province)->findAll();
    }

    /**
     * Get organizations by country
     *
     * @param string $country
     * @return array
     */
    public function getByCountry(string $country)
    {
        return $this->where('location_lock_country', $country)->findAll();
    }

    /**
     * Get location locked organizations
     *
     * @return array
     */
    public function getLocationLockedOrganizations()
    {
        return $this->where('is_locationlocked', 1)->findAll();
    }

    /**
     * Get organizations by license status
     *
     * @param string $status
     * @return array
     */
    public function getByLicenseStatus(string $status)
    {
        return $this->where('license_status', $status)->findAll();
    }

    /**
     * Search organizations by name or code
     *
     * @param string $search
     * @return array
     */
    public function searchOrganizations(string $search)
    {
        return $this->groupStart()
                    ->like('org_name', $search)
                    ->orLike('org_code', $search)
                    ->groupEnd()
                    ->findAll();
    }

    /**
     * Get organization statistics
     *
     * @return array
     */
    public function getOrganizationStats()
    {
        $stats = [];

        // Total organizations
        $stats['total'] = $this->countAllResults(false);

        // Active organizations
        $stats['active'] = $this->where('is_active', 1)->countAllResults(false);

        // Inactive organizations
        $stats['inactive'] = $this->where('is_active', 0)->countAllResults(false);

        // Location locked organizations
        $stats['location_locked'] = $this->where('is_locationlocked', 1)->countAllResults(false);

        // Organizations by license status
        $licenseStats = $this->select('license_status, COUNT(*) as count')
                             ->where('license_status IS NOT NULL')
                             ->groupBy('license_status')
                             ->findAll();

        $stats['by_license_status'] = [];
        foreach ($licenseStats as $stat) {
            $stats['by_license_status'][$stat['license_status']] = $stat['count'];
        }

        return $stats;
    }

    /**
     * Activate organization
     *
     * @param int $id
     * @param int $updatedBy
     * @return bool
     */
    public function activateOrganization(int $id, int $updatedBy = null)
    {
        $data = ['is_active' => 1];
        if ($updatedBy) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }

    /**
     * Deactivate organization
     *
     * @param int $id
     * @param int $updatedBy
     * @return bool
     */
    public function deactivateOrganization(int $id, int $updatedBy = null)
    {
        $data = ['is_active' => 0];
        if ($updatedBy) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }

    /**
     * Update license status
     *
     * @param int $id
     * @param string $status
     * @param int $updatedBy
     * @return bool
     */
    public function updateLicenseStatus(int $id, string $status, int $updatedBy = null)
    {
        $data = ['license_status' => $status];
        if ($updatedBy) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }
}

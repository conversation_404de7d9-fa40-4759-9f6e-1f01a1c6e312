<?= $this->extend('templates/home_template') ?>

<?= $this->section('css') ?>
<style>
    /* Page-specific styles for job details page */
    .card-header {
        background-color: rgba(240, 15, 0, 0.05);
        border-bottom: 1px solid rgba(240, 15, 0, 0.1);
        font-weight: 600;
    }

    .detail-label {
        font-weight: 600;
        color: var(--text-secondary);
    }

    .badge-classification {
        background-color: var(--yellow);
        color: var(--black);
    }

    .deadline-banner {
        background-color: rgba(240, 15, 0, 0.1);
        border-left: 4px solid var(--red);
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container py-4">
        <!-- Back button -->
        <div class="mb-4">
            <a href="<?= base_url('jobs') ?>" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> Back to Jobs
            </a>
        </div>

        <!-- Position Header -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h1 class="h2 mb-2"><?= esc($position['designation']) ?></h1>
                        <p class="mb-2">
                            <span class="badge badge-classification"><?= esc($position['classification']) ?></span>
                            <?php if (isset($position['is_internal'])): ?>
                                <?php if ($position['is_internal'] == 1): ?>
                                    <span class="badge bg-warning text-dark ms-2">Internal</span>
                                <?php else: ?>
                                    <span class="badge bg-primary ms-2">External</span>
                                <?php endif; ?>
                            <?php endif; ?>
                            <span class="text-muted ms-2">Reference: <?= esc($position['position_reference']) ?></span>
                        </p>
                        <p class="mb-0">
                            <strong><i class="bi bi-building"></i> Organization:</strong> <?= esc($position['org_name']) ?>
                        </p>
                        <p class="mb-0">
                            <strong><i class="bi bi-geo-alt"></i> Location:</strong> <?= esc($position['location']) ?>
                        </p>
                        <p class="mb-0">
                            <strong><i class="bi bi-cash"></i> Salary:</strong> <?= esc($position['annual_salary']) ?>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <?php if (!empty($position['publish_date_to'])): ?>
                        <div class="deadline-banner">
                            <h5 class="mb-2">Application Deadline</h5>
                            <p class="mb-0 fw-bold"><?= date('d F Y', strtotime($position['publish_date_to'])) ?></p>
                            <small class="text-muted">Applications close at <?= date('g:i A', strtotime($position['publish_date_to'])) ?></small>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($position['jd_filepath'])): ?>
                            <a href="<?= base_url($position['jd_filepath']) ?>" class="btn btn-primary w-100" target="_blank">
                                <i class="bi bi-file-earmark-pdf"></i> Download Job Description
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Position Details -->
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Applicant Notice -->
                <?php if (!empty($position['applicant_notice'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-bell"></i> Important Notice for Applicants
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info mb-0">
                            <?= nl2br(esc($position['applicant_notice'])) ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Qualifications -->
                <?php if (!empty($position['qualifications'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Qualifications</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['qualifications'])) ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Knowledge -->
                <?php if (!empty($position['knowledge'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Knowledge</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['knowledge'])) ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Skills & Competencies -->
                <?php if (!empty($position['skills_competencies'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Skills & Competencies</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['skills_competencies'])) ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Job Experiences -->
                <?php if (!empty($position['job_experiences'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Required Experience</div>
                    <div class="card-body">
                        <?= nl2br(esc($position['job_experiences'])) ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Applicant Information -->
                <?php if (!empty($position['applicant_information'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-info-circle"></i> Applicant Information
                    </div>
                    <div class="card-body">
                        <?= $position['applicant_information'] ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- Exercise Information -->
                <?php if (!empty($position['exercise_name'])): ?>
                <div class="card mb-4">
                    <div class="card-header">Exercise Information</div>
                    <div class="card-body">
                        <p class="mb-2">
                            <span class="detail-label">Exercise Name:</span><br>
                            <?= esc($position['exercise_name']) ?>
                        </p>
                        <?php if (!empty($position['publish_date_from'])): ?>
                        <p class="mb-2">
                            <span class="detail-label">Published Date:</span><br>
                            <?= date('d F Y', strtotime($position['publish_date_from'])) ?>
                        </p>
                        <?php endif; ?>
                        <?php if (!empty($position['publish_date_to'])): ?>
                        <p class="mb-0">
                            <span class="detail-label">Closing Date:</span><br>
                            <?= date('d F Y \a\t g:i A', strtotime($position['publish_date_to'])) ?>
                        </p>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Organization Information -->
                <div class="card mb-4">
                    <div class="card-header">Organization Information</div>
                    <div class="card-body">
                        <p class="mb-2">
                            <span class="detail-label">Organization:</span><br>
                            <?= !empty($position['org_name']) ? esc($position['org_name']) : 'Not specified' ?>
                        </p>
                        <p class="mb-0">
                            <span class="detail-label">Organization Code:</span><br>
                            <?= !empty($position['org_code']) ? esc($position['org_code']) : 'Not specified' ?>
                        </p>
                    </div>
                </div>

                <!-- Apply Now Card -->
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="mb-3">Interested in this position?</h5>
                        <p class="mb-3">Create an account or login to apply for this position.</p>
                        <a href="<?= base_url('applicant/login') ?>" class="btn btn-primary w-100 mb-2">Login to Apply</a>
                        <a href="<?= base_url('applicant/register') ?>" class="btn btn-outline-primary w-100">Create Account</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

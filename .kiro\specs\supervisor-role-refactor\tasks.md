# Implementation Plan

- [ ] 1. Create database migration script for supervisor capability
  - Create SQL script to add `is_supervisor` field to users table
  - Create migration script to update existing supervisor role users
  - Create rollback script for migration reversal
  - _Requirements: 1.1, 1.4_

- [x] 2. Update UsersModel for supervisor capability support
  - Add `is_supervisor` to `$allowedFields` array
  - Remove 'supervisor' from role validation rules
  - Update validation messages to reflect new role options
  - _Requirements: 2.2, 4.1, 4.2, 4.3_

- [ ] 3. Add supervisor capability methods to UsersModel





  - Implement `getSupervisors()` method to get all supervisor users
  - Implement `getUsersWithSupervisorCapability($orgId)` method
  - Implement `setSupervisorCapability($userId, $isSupervisor)` method
  - Update `getUserStatistics()` method to include supervisor capability counts
  - _Requirements: 2.2, 5.4_

- [ ] 4. Update Dakoii controller for supervisor capability management
  - Update user creation validation to exclude 'supervisor' role (currently allows supervisor in validation)
  - Update user editing validation to exclude 'supervisor' role (currently allows supervisor in validation)
  - Add supervisor capability handling in user management forms
  - _Requirements: 2.1, 4.1, 4.2_

- [ ] 5. Update authentication controllers for supervisor capability
  - Update HomeAuthController to use `is_supervisor` field instead of role check (currently checks for 'supervisor' role)
  - Update session data to include `is_supervisor` information
  - Update access control logic in AdminController (currently checks for 'supervisor' role)
  - _Requirements: 2.1, 5.2, 5.3_

- [ ] 6. Update Auth filter for supervisor capability checks
  - Modify Auth.php filter to check `is_supervisor` field (currently allows 'supervisor' role)
  - Update authorization logic for supervisor-specific permissions
  - Test filter functionality with new capability system
  - _Requirements: 5.3_

- [ ] 7. Execute database migration and verify data integrity
  - Run migration script on development database
  - Verify all users with role='supervisor' are migrated correctly
  - Test that new `is_supervisor` field works as expected
  - Update database schema to remove 'supervisor' from role enum
  - _Requirements: 1.1, 1.4_

- [ ] 8. Update user management views for supervisor capability
  - Add supervisor capability checkbox/toggle to user creation forms
  - Add supervisor capability checkbox/toggle to user editing forms
  - Update user list displays to show supervisor capability separately from role
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [ ] 9. Update user display views and badges
  - Modify dakoii_ddash.php to display supervisor capability badge
  - Update dakoii_open_org.php to show supervisor status
  - Update other user display views to reflect supervisor capability
  - _Requirements: 3.1, 3.4_

- [ ] 10. Comprehensive testing of supervisor capability system
  - Test user authentication with supervisor capabilities
  - Test user management functionality (create, edit, delete)
  - Test access control and authorization
  - Test user interface displays and forms
  - _Requirements: 2.1, 3.1, 5.1, 5.3_
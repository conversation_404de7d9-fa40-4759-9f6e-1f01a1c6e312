# DERS Project Structure

## CodeIgniter 4 MVC Architecture

### Core Application Structure (`app/`)
```
app/
├── Controllers/          # Request handlers and business logic
├── Models/              # Database interaction and data logic
├── Views/               # Presentation layer templates
├── Config/              # Application configuration
├── Database/            # Migrations, seeds, and database files
├── Filters/             # Request/response filters
├── Helpers/             # Utility functions
├── Libraries/           # Custom libraries
├── Services/            # Service layer classes
└── Language/            # Internationalization files
```

## Controller Organization Patterns

### Naming Conventions
- Controllers use PascalCase with "Controller" suffix
- Methods use camelCase
- Follow RESTful patterns where applicable

### Controller Categories
- **Admin Controllers**: `AdminController`, `DakoiiController` - System administration
- **Applicant Controllers**: `ApplicantController`, `ApplicantProfileController` - User-facing functionality
- **Process Controllers**: `ApplicationPreScreeningController`, `RatingProcessController` - Workflow management
- **Report Controllers**: `ReportsMainController`, `ReportsApplicationController` - Data reporting
- **API Controllers**: `Api.php`, `RatingAIController` - API endpoints

## Model Patterns

### Naming Convention
- Models use PascalCase with "Model" suffix
- Follow table name + "Model" pattern (e.g., `ApplicantsModel` for `applicants` table)

### Model Categories
- **Core Entities**: `ApplicantsModel`, `UsersModel`, `ExerciseModel`
- **Application Data**: `AppxApplication*Model` - Application-specific data
- **Geographic**: `Geo*Model` - Location-based data
- **Configuration**: `EducationLevelsModel`, `PositionsModel`

## File Upload Structure
```
public/uploads/          # Web-accessible uploads
writable/uploads/        # Protected uploads
writable/logs/           # Application logs
writable/cache/          # Cache files
```

## Database Backup Strategy
- SQL backups stored in `db_backups/`
- Include documentation files alongside backups
- Version-controlled schema updates

## Configuration Management
- Environment-specific settings in `.env`
- Application config in `app/Config/`
- Database configuration separate from code
- Upload paths and limits configurable

## Security Considerations
- Entry point through `public/` directory only
- Application files above web root
- `.htaccess` files for access control
- Session-based authentication with role checking
<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening') ?>">Pre-Screening</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>">Pre-Screening Applicants</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pre-Screening Profile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applicants
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Two Column Layout -->
<div class="row">
    <!-- Left Column - Applicant Profile -->
    <div class="col-lg-8">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user"></i> Applicant Profile: <?= esc($applicant_name) ?>
                </h5>
            </div>
            <div class="card-body p-0">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active text-dark" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">
                            <i class="fas fa-user-circle"></i> Applicant Details
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link text-dark" id="files-tab" data-bs-toggle="tab" data-bs-target="#files" type="button" role="tab" aria-controls="files" aria-selected="false">
                            <i class="fas fa-file-pdf"></i> Files (<?= count($applicant_files) ?>)
                        </button>
                    </li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content" id="profileTabsContent">
                    <!-- Applicant Details Tab -->
                    <div class="tab-pane fade show active" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div class="p-4">
                            <?php if (!empty($applications)): ?>
                                <?php $firstApp = $applications[0]; ?>

                                <!-- 1. Personal Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-user me-2"></i>Personal Information
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Basic details and contact info</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Full Name:</strong> <?= esc($firstApp['first_name'] . ' ' . $firstApp['last_name']) ?></p>
                                                <p><strong>Gender:</strong> <?= esc($firstApp['gender']) ?></p>
                                                <p><strong>Date of Birth:</strong>
                                                    <span id="dobDisplay"><?= esc($firstApp['date_of_birth']) ?></span>
                                                    <span id="ageDisplay" class="badge bg-secondary ms-2"></span>
                                                </p>
                                                <p><strong>Place of Origin:</strong> <?= esc($firstApp['place_of_origin']) ?></p>
                                                <p><strong>Citizenship:</strong> <?= esc($firstApp['citizenship']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Email:</strong> <?= esc($firstApp['email_address']) ?></p>
                                                <p><strong>Contact Details:</strong> <?= esc($firstApp['contact_details']) ?></p>
                                                <p><strong>Address:</strong> <?= esc($firstApp['location_address']) ?></p>
                                                <p><strong>Marital Status:</strong> <?= esc($firstApp['marital_status']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 2. Documents & ID -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-id-card me-2"></i>Documents & ID
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Identification and records</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>ID Numbers:</strong> <?= esc($firstApp['id_numbers']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Offence Convicted:</strong> <?= esc($firstApp['offence_convicted']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 3. Employment -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-briefcase me-2"></i>Employment
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Current work information</small>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Current Employer:</strong> <?= esc($firstApp['current_employer']) ?></p>
                                                <p><strong>Current Position:</strong> <?= esc($firstApp['current_position']) ?></p>
                                                <p><strong>Current Salary:</strong> <?= esc($firstApp['current_salary']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>Public Servant:</strong>
                                                    <span class="badge bg-<?= $firstApp['is_public_servant'] ? 'success' : 'secondary' ?>">
                                                        <?= $firstApp['is_public_servant'] ? 'Yes' : 'No' ?>
                                                    </span>
                                                </p>
                                                <?php if ($firstApp['is_public_servant']): ?>
                                                    <p><strong>File Number:</strong> <?= esc($firstApp['public_service_file_number']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 4. Work Experience -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-history me-2"></i>Work Experience
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Previous employment history</small>
                                        <div class="bg-light p-3 rounded">
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Work experience details are available in uploaded documents and files.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 5. Education -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-graduation-cap me-2"></i>Education
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Academic qualifications</small>
                                        <div class="bg-light p-3 rounded">
                                            <p class="text-muted mb-0">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Education details are available in uploaded documents and files.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 6. Family Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-users me-2"></i>Family Information
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Family details and dependents</small>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mb-3">
                                                    <strong>Children:</strong>
                                                    <div id="childrenDisplay" class="mt-2">
                                                        <!-- Children will be displayed here in table format -->
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Referees -->
                                        <?php if (!empty($firstApp['referees'])): ?>
                                        <div class="mt-4">
                                            <strong>Referees:</strong>
                                            <div class="bg-light p-3 rounded mt-2">
                                                <?= nl2br(esc($firstApp['referees'])) ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- 7. Achievements -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-red border-bottom pb-2 mb-3">
                                            <i class="fas fa-star me-2"></i>Achievements
                                        </h6>
                                        <small class="text-muted mb-3 d-block">Awards and accomplishments</small>
                                        <div class="row">
                                            <?php if (!empty($firstApp['publications'])): ?>
                                            <div class="col-md-6">
                                                <strong>Publications:</strong>
                                                <div class="bg-light p-3 rounded mt-2">
                                                    <?= nl2br(esc($firstApp['publications'])) ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (!empty($firstApp['awards'])): ?>
                                            <div class="col-md-6">
                                                <strong>Awards:</strong>
                                                <div class="bg-light p-3 rounded mt-2">
                                                    <?= nl2br(esc($firstApp['awards'])) ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (empty($firstApp['publications']) && empty($firstApp['awards'])): ?>
                                            <div class="col-12">
                                                <div class="bg-light p-3 rounded">
                                                    <p class="text-muted mb-0">
                                                        <i class="fas fa-info-circle me-1"></i>
                                                        No achievements or awards information provided.
                                                    </p>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Additional Information -->
                                        <div class="mt-4">
                                            <strong>How did you hear about us:</strong>
                                            <div class="bg-light p-3 rounded mt-2">
                                                <?= esc($firstApp['how_did_you_hear_about_us']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> No application details found.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Files Tab -->
                    <div class="tab-pane fade" id="files" role="tabpanel" aria-labelledby="files-tab">
                        <div class="p-4">
                            <?php if (!empty($applicant_files)): ?>
                                <div class="mb-3">
                                    <h6 class="text-red">PDF Files Viewer</h6>
                                    <p class="text-muted">All PDF files are merged and displayed below. Scroll through to view all documents.</p>
                                </div>

                                <!-- AI Analysis Button -->
                                <div id="aiAnalysisSection" class="mb-4" style="display: none;">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-robot me-2"></i>AI Analysis
                                            </h6>
                                        </div>
                                        <div class="card-body text-center">
                                            <p class="text-muted mb-3">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Files have been converted to images. Click below to analyze with AI.
                                            </p>
                                            <button type="button" class="btn btn-primary btn-lg" id="analyzeWithAIBtn" onclick="analyzeWithAI()">
                                                <i class="fas fa-brain me-2"></i>Analyze with AI
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- AI Analysis Results -->
                                <div id="aiAnalysisResults" class="mb-4" style="display: none;">
                                    <!-- Results will be displayed here -->
                                </div>

                                <!-- PDF Viewer Container -->
                                <div id="pdfViewerContainer" style="height: 800px; border: 1px solid #ddd; border-radius: 8px; overflow-y: auto;">
                                    <div id="pdfViewer" class="p-3">
                                        <div class="text-center p-4">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading PDFs...</span>
                                            </div>
                                            <p class="mt-2">Loading PDF files...</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Files List -->
                                <div class="mt-4">
                                    <h6 class="text-red">Files List</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th class="text-white">#</th>
                                                    <th class="text-white">File Title</th>
                                                    <th class="text-white">Description</th>
                                                    <th class="text-white">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($applicant_files as $index => $file): ?>
                                                <tr>
                                                    <td><?= $index + 1 ?></td>
                                                    <td>
                                                        <i class="fas fa-file-pdf text-danger"></i>
                                                        <?= esc($file['file_title']) ?>
                                                    </td>
                                                    <td><?= esc($file['file_description']) ?></td>
                                                    <td>
                                                        <div class="d-flex gap-1">
                                                            <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-external-link-alt"></i> Open
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No files uploaded for this applicant.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column - Pre-Screening Criteria -->
    <div class="col-lg-4">
        <div class="card hover-card sticky-criteria-card">
            <div class="card-header bg-yellow text-black">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-check"></i> Pre-Screening Criteria
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($pre_screen_criteria)): ?>
                    <div class="criteria-list">
                        <?php foreach ($pre_screen_criteria as $index => $criteria): ?>
                            <div class="criteria-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="text-red mb-0">
                                        <span class="badge bg-primary me-2"><?= $index + 1 ?></span>
                                        <?= esc($criteria['name'] ?? 'Criteria ' . ($index + 1)) ?>
                                    </h6>
                                </div>
                                <?php if (!empty($criteria['description'])): ?>
                                    <p class="text-muted mb-2"><?= esc($criteria['description']) ?></p>
                                <?php endif; ?>



                                <div class="criteria-actions mt-2">
                                    <div class="btn-group btn-group-sm w-100" role="group">
                                        <button type="button" class="btn btn-outline-success" id="passBtn_<?= $index ?>" onclick="markCriteria(<?= $index ?>, 'pass')">
                                            <i class="fas fa-check"></i> Pass
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" id="failBtn_<?= $index ?>" onclick="markCriteria(<?= $index ?>, 'fail')">
                                            <i class="fas fa-times"></i> Fail
                                        </button>
                                    </div>

                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Overall Assessment -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6 class="text-red">Overall Assessment</h6>
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-success" id="passedCount">0</div>
                                    <small class="text-muted">Passed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-danger" id="failedCount">0</div>
                                    <small class="text-muted">Failed</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="criteria-summary">
                                    <div class="h4 text-warning" id="pendingCount"><?= count($pre_screen_criteria) ?></div>
                                    <small class="text-muted">Pending</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="progressBar"></div>
                            </div>
                            <small class="text-muted">Completion Progress</small>
                        </div>

                        <!-- Existing Pre-Screening Information -->
                        <?php if (isset($existing_prescreening) && !empty($existing_prescreening['pre_screened_at'])): ?>
                            <div class="alert alert-info mt-3">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>Existing Pre-Screening Information
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1">
                                            <strong>Status:</strong>
                                            <span class="badge <?= $existing_prescreening['pre_screened_status'] === 'passed' ? 'bg-success' : 'bg-danger' ?>">
                                                <?= ucfirst(esc($existing_prescreening['pre_screened_status'])) ?>
                                            </span>
                                        </p>
                                        <p class="mb-1">
                                            <strong>Date:</strong> <?= date('M d, Y \a\t g:i A', strtotime($existing_prescreening['pre_screened_at'])) ?>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1">
                                            <strong>Screened By:</strong>
                                            <?php if (!empty($screened_by_username)): ?>
                                                <?= esc($screened_by_username) ?>
                                            <?php else: ?>
                                                User ID <?= esc($existing_prescreening['pre_screened_by']) ?>
                                            <?php endif; ?>
                                        </p>
                                        <?php if (!empty($existing_prescreening['pre_screened_remarks'])): ?>
                                            <p class="mb-0">
                                                <strong>Previous Remarks:</strong><br>
                                                <small class="text-muted"><?= esc($existing_prescreening['pre_screened_remarks']) ?></small>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Pre-Screening Form -->
                        <form id="preScreeningForm" class="mt-4" method="post" action="<?= base_url('application_pre_screening/save_prescreening_results') ?>">
                            <?= csrf_field() ?>
                            <input type="hidden" name="applicant_id" value="<?= $applicant_id ?>">
                            <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                            <input type="hidden" name="pre_screened_criteria_results" id="criteriaResultsData">

                            <!-- Display Flash Messages -->
                            <?php if (session()->has('success')): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?= session('success') ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <?php if (session()->has('error')): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <?= session('error') ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <?php if (session()->has('validation_errors')): ?>
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Validation Errors:</strong>
                                    <ul class="mb-0 mt-2">
                                        <?php foreach (session('validation_errors') as $error): ?>
                                            <li><?= esc($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label for="pre_screened_status" class="form-label">
                                    <strong>Pre-Screening Status <span class="text-danger">*</span></strong>
                                </label>
                                <select class="form-select" name="pre_screened_status" id="pre_screened_status" required>
                                    <option value="">Select Status</option>
                                    <option value="passed" <?= (isset($existing_prescreening['pre_screened_status']) && $existing_prescreening['pre_screened_status'] === 'passed') ? 'selected' : '' ?>>Passed</option>
                                    <option value="failed" <?= (isset($existing_prescreening['pre_screened_status']) && $existing_prescreening['pre_screened_status'] === 'failed') ? 'selected' : '' ?>>Failed</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="pre_screened_remarks" class="form-label">
                                    <strong>Remarks</strong>
                                </label>
                                <textarea class="form-control" name="pre_screened_remarks" id="pre_screened_remarks"
                                          rows="4" placeholder="Enter your pre-screening remarks and observations..."><?= isset($existing_prescreening['pre_screened_remarks']) ? esc($existing_prescreening['pre_screened_remarks']) : '' ?></textarea>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="savePreScreeningBtn">
                                    <i class="fas fa-save me-2"></i>Save Pre-Screening Results
                                </button>
                            </div>
                        </form>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No pre-screening criteria defined for this exercise.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

<style>
/* Fix tab text colors for better visibility */
.nav-tabs .nav-link {
    color: #333 !important;
}

.nav-tabs .nav-link.active {
    color: #000 !important;
    font-weight: 600;
}

.nav-tabs .nav-link:hover {
    color: #F00F00 !important;
}

/* Sticky Pre-Screening Criteria Card */
.sticky-criteria-card {
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
    z-index: 1000;
}
</style>

<script>
// Criteria tracking
let criteriaResults = {};
const totalCriteria = <?= count($pre_screen_criteria) ?>;
const preScreenCriteria = <?= json_encode($pre_screen_criteria) ?>;

$(document).ready(function() {
    // Load PDFs immediately on page load
    loadAllPDFsNative();

    // Initialize criteria tracking
    updateCriteriaSummary();
});

// Simple variables for tracking conversion and AI analysis
let convertedImages = [];
let totalFilesConverted = 0;
let totalFilesToConvert = 0;
const applicantFiles = <?= json_encode($applicant_files) ?>;

// Initialize conversion tracking
function initializeConversionTracking() {
    totalFilesToConvert = applicantFiles.length;
    totalFilesConverted = 0;
    convertedImages = [];
    console.log('Conversion tracking initialized for', totalFilesToConvert, 'files');
}

// Track when a file is converted to images
function onFileConverted(fileData) {
    // Prevent duplicate counting
    if (fileData.counted) return;
    fileData.counted = true;

    totalFilesConverted++;

    console.log(`File converted: ${fileData.fileName} (${totalFilesConverted}/${totalFilesToConvert})`);

    // Check if all files are converted
    if (totalFilesConverted >= totalFilesToConvert) {
        showAIAnalysisButton();
    }
}

// Show the AI Analysis button (PDFs are ready immediately)
function showAIAnalysisButton() {
    const aiAnalysisSection = document.getElementById('aiAnalysisSection');
    if (aiAnalysisSection) {
        aiAnalysisSection.style.display = 'block';
        console.log('PDFs loaded. AI Analysis button is now available.');
    }
}

// Show analysis progress indicator
function showAnalysisProgress(totalFiles) {
    const progressContainer = document.getElementById('aiAnalysisResults');
    if (progressContainer) {
        progressContainer.style.display = 'block';
        progressContainer.innerHTML = `
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cog fa-spin me-2"></i>AI Analysis in Progress
                    </h6>
                </div>
                <div class="card-body">
                    <div class="progress mb-3" style="height: 25px;">
                        <div id="analysisProgressBar" class="progress-bar bg-info progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            0%
                        </div>
                    </div>
                    <p id="analysisProgressText" class="mb-0 text-center">
                        <i class="fas fa-file-pdf text-danger me-2"></i>
                        Preparing to analyze ${totalFiles} PDF file(s)...
                    </p>
                </div>
            </div>
        `;
    }
}

// Update analysis progress
function updateAnalysisProgress(current, total, message) {
    const progressBar = document.getElementById('analysisProgressBar');
    const progressText = document.getElementById('analysisProgressText');

    if (progressBar && progressText) {
        const percentage = Math.round((current / total) * 100);
        progressBar.style.width = `${percentage}%`;
        progressBar.setAttribute('aria-valuenow', percentage);
        progressBar.textContent = `${percentage}%`;
        progressText.innerHTML = `<i class="fas fa-brain text-primary me-2"></i>${message}`;
    }
}

// Hide analysis progress indicator
function hideAnalysisProgress() {
    // Progress will be replaced by results, so no need to explicitly hide
}

// Consolidate multiple analysis results into a single result
function consolidateAnalysisResults(analysisResults) {
    if (analysisResults.length === 1) {
        return analysisResults[0];
    }

    // Initialize consolidated result structure
    const consolidated = {
        analysis_summary: {
            overall_recommendation: 'REVIEW',
            confidence_score: '0%',
            key_findings: []
        },
        criteria_evaluation: []
    };

    // Collect all criteria evaluations
    const criteriaMap = new Map();
    let totalConfidence = 0;
    let confidenceCount = 0;
    const allFindings = [];

    analysisResults.forEach((result, index) => {
        // Collect key findings
        if (result.analysis_summary?.key_findings) {
            allFindings.push(...result.analysis_summary.key_findings);
        }

        // Process criteria evaluations
        if (result.criteria_evaluation) {
            result.criteria_evaluation.forEach((evaluation, criteriaIndex) => {
                if (!criteriaMap.has(criteriaIndex)) {
                    criteriaMap.set(criteriaIndex, {
                        criterion_number: evaluation.criterion_number || (criteriaIndex + 1),
                        evaluation_result: 'DOES NOT MEET',
                        detailed_analysis: [],
                        evidence_found: [],
                        confidence_level: 'Medium'
                    });
                }

                const existing = criteriaMap.get(criteriaIndex);

                // Combine evidence and analysis
                if (evaluation.detailed_analysis) {
                    existing.detailed_analysis.push(`[Chunk ${index + 1}] ${evaluation.detailed_analysis}`);
                }
                if (evaluation.evidence_found) {
                    existing.evidence_found.push(...evaluation.evidence_found);
                }

                // If any chunk says MEETS, consider it as MEETS
                if (evaluation.evaluation_result === 'MEETS') {
                    existing.evaluation_result = 'MEETS';
                }
            });
        }

        // Extract confidence scores
        if (result.analysis_summary?.confidence_score) {
            const score = parseInt(result.analysis_summary.confidence_score.replace('%', ''));
            if (!isNaN(score)) {
                totalConfidence += score;
                confidenceCount++;
            }
        }
    });

    // Finalize consolidated criteria evaluations
    criteriaMap.forEach((criteria, index) => {
        consolidated.criteria_evaluation.push({
            criterion_number: criteria.criterion_number,
            evaluation_result: criteria.evaluation_result,
            detailed_analysis: criteria.detailed_analysis.join(' '),
            evidence_found: [...new Set(criteria.evidence_found)], // Remove duplicates
            confidence_level: criteria.confidence_level
        });
    });

    // Calculate overall recommendation
    const passCount = consolidated.criteria_evaluation.filter(c => c.evaluation_result === 'MEETS').length;
    const totalCriteria = consolidated.criteria_evaluation.length;

    if (passCount === totalCriteria) {
        consolidated.analysis_summary.overall_recommendation = 'PROCEED';
    } else if (passCount === 0) {
        consolidated.analysis_summary.overall_recommendation = 'REJECT';
    } else {
        consolidated.analysis_summary.overall_recommendation = 'REVIEW';
    }

    // Calculate average confidence
    if (confidenceCount > 0) {
        const avgConfidence = Math.round(totalConfidence / confidenceCount);
        consolidated.analysis_summary.confidence_score = `${avgConfidence}%`;
    }

    // Consolidate key findings (remove duplicates)
    consolidated.analysis_summary.key_findings = [...new Set(allFindings)].slice(0, 5); // Top 5 unique findings

    console.log('Consolidated analysis results:', consolidated);
    return consolidated;
}

// Convert PDF file to base64 for API submission
async function convertPDFToBase64(filePath) {
    try {
        const response = await fetch(filePath);
        if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        // Convert to base64
        let binary = '';
        const len = uint8Array.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }

        return btoa(binary);
    } catch (error) {
        console.error('Error converting PDF to base64:', error);
        throw new Error(`Failed to convert PDF to base64: ${error.message}`);
    }
}

// AI Analysis function - analyzes PDF files in manageable chunks
async function analyzeWithAI() {
    const analyzeBtn = document.getElementById('analyzeWithAIBtn');
    const originalBtnText = analyzeBtn.innerHTML;

    // Show loading state
    analyzeBtn.disabled = true;
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';

    try {
        const files = <?= json_encode($applicant_files) ?>;

        if (files.length === 0) {
            throw new Error('No PDF files available for analysis');
        }

        // Show progress indicator
        showAnalysisProgress(files.length);

        // Analyze PDFs in chunks to avoid timeout
        const chunkSize = 2; // Analyze 2 PDFs at a time
        const allAnalysisResults = [];

        for (let i = 0; i < files.length; i += chunkSize) {
            const chunk = files.slice(i, i + chunkSize);
            const chunkNumber = Math.floor(i / chunkSize) + 1;
            const totalChunks = Math.ceil(files.length / chunkSize);

            updateAnalysisProgress(chunkNumber, totalChunks, `Processing chunk ${chunkNumber} of ${totalChunks}...`);

            // Prepare chunk data
            const chunkPdfData = [];
            for (const file of chunk) {
                const filePath = '<?= base_url() ?>' + file.file_path;
                const pdfBase64 = await convertPDFToBase64(filePath);
                chunkPdfData.push({
                    file_name: file.file_title || file.original_filename,
                    pdf_base64: pdfBase64
                });
            }

            // Analyze this chunk
            const chunkResult = await sendToBackendForPrescreeningPDFs(chunkPdfData, preScreenCriteria);
            allAnalysisResults.push(chunkResult);

            // Small delay between chunks to prevent overwhelming the API
            if (i + chunkSize < files.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // Consolidate all chunk results
        updateAnalysisProgress(100, 100, 'Consolidating results...');
        const consolidatedResult = consolidateAnalysisResults(allAnalysisResults);

        // Hide progress indicator
        hideAnalysisProgress();

        // Display results (AI results are shown separately - no form auto-population)
        displayAIAnalysisResults(consolidatedResult);

    } catch (error) {
        console.error('AI Analysis Error:', error);
        hideAnalysisProgress();
        alert('AI Analysis failed: ' + error.message);
    } finally {
        // Restore button
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = originalBtnText;
    }
}

// Display AI Analysis Results - separate from criteria form with copy functionality
function displayAIAnalysisResults(analysisResult) {
    const resultsContainer = document.getElementById('aiAnalysisResults');
    if (!resultsContainer) return;

    resultsContainer.style.display = 'block';

    // Create formatted text for copying
    const copyText = formatAIResultsForCopy(analysisResult);

    let resultsHTML = `
        <div class="card border-primary">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-robot me-2"></i>AI Analysis Results
                </h6>
                <button type="button" class="btn btn-light btn-sm" onclick="copyAIResults()" title="Copy AI Analysis">
                    <i class="fas fa-copy me-1"></i>Copy Results
                </button>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> This AI analysis is for reference only. Please review the results and manually select Pass/Fail for each criterion based on your professional judgment.
                </div>
    `;

    // Display criteria evaluation results with detailed remarks
    if (analysisResult.criteria_evaluation && analysisResult.criteria_evaluation.length > 0) {
        resultsHTML += `<h6 class="text-primary mb-3">Pre-Screening Criteria Evaluation:</h6>`;

        analysisResult.criteria_evaluation.forEach((evaluation, index) => {
            const statusClass = evaluation.evaluation_result === 'MEETS' ? 'success' :
                              evaluation.evaluation_result === 'DOES NOT MEET' ? 'danger' : 'warning';

            const statusText = evaluation.evaluation_result === 'MEETS' ? 'MEETS' :
                             evaluation.evaluation_result === 'DOES NOT MEET' ? 'DOES NOT MEET' : 'PARTIAL';

            resultsHTML += `
                <div class="mb-4 p-3 border rounded ${statusClass === 'success' ? 'border-success' : statusClass === 'danger' ? 'border-danger' : 'border-warning'}">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h6 class="mb-0 text-dark">
                            <span class="badge bg-primary me-2">${index + 1}</span>
                            ${evaluation.criterion_title || preScreenCriteria[index]?.name}
                        </h6>
                        <span class="badge bg-${statusClass} fs-6">${statusText}</span>
                    </div>

                    <!-- AI Analysis & Justification -->
                    <div class="mb-3">
                        <h6 class="text-secondary mb-2">
                            <i class="fas fa-search me-1"></i>AI Analysis:
                        </h6>
                        <p class="mb-2 text-dark">${evaluation.detailed_analysis || 'Analysis completed.'}</p>
                    </div>

                    <!-- Evidence Found -->
                    ${evaluation.evidence_found && evaluation.evidence_found.length > 0 ? `
                    <div class="mb-3">
                        <h6 class="text-secondary mb-2">
                            <i class="fas fa-file-alt me-1"></i>Evidence Found:
                        </h6>
                        <ul class="list-unstyled ms-3">
                            ${evaluation.evidence_found.map(evidence => `
                                <li class="mb-1">
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    <small class="text-dark">${evidence}</small>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                    ` : `
                    <div class="mb-3">
                        <h6 class="text-secondary mb-2">
                            <i class="fas fa-exclamation-triangle me-1"></i>Evidence:
                        </h6>
                        <p class="text-muted ms-3"><small>No specific evidence found in the documents.</small></p>
                    </div>
                    `}

                    <!-- AI Recommendation -->
                    ${evaluation.recommendation ? `
                    <div class="alert alert-${statusClass === 'success' ? 'success' : statusClass === 'danger' ? 'danger' : 'warning'} mb-0 py-2">
                        <strong><i class="fas fa-lightbulb me-1"></i>AI Recommendation:</strong>
                        ${evaluation.recommendation}
                    </div>
                    ` : ''}
                </div>
            `;
        });
    }

    // Display overall recommendation with summary
    if (analysisResult.analysis_summary?.overall_recommendation) {
        const overallClass = analysisResult.analysis_summary.overall_recommendation === 'RECOMMENDED' ? 'success' : 'danger';
        const passedCount = analysisResult.criteria_evaluation?.filter(e => e.evaluation_result === 'MEETS').length || 0;
        const totalCount = analysisResult.criteria_evaluation?.length || 0;

        resultsHTML += `
            <div class="alert alert-${overallClass} mt-4">
                <h6 class="mb-2">
                    <i class="fas fa-clipboard-check me-2"></i>Overall AI Assessment:
                </h6>
                <p class="mb-2">
                    <strong>Result:</strong> ${analysisResult.analysis_summary.overall_recommendation}<br>
                    <strong>Summary:</strong> ${passedCount} out of ${totalCount} criteria met
                </p>
                ${analysisResult.analysis_summary.confidence_score ? `
                    <small class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>
                        AI Confidence: ${analysisResult.analysis_summary.confidence_score}
                    </small>
                ` : ''}
            </div>
        `;
    }

    resultsHTML += `
            </div>
        </div>
    `;

    resultsContainer.innerHTML = resultsHTML;

    // Store the copy text globally for the copy function
    window.aiResultsCopyText = copyText;
}

// Format AI results for copying to clipboard
function formatAIResultsForCopy(analysisResult) {
    let copyText = "AI ANALYSIS RESULTS\n";
    copyText += "==================\n\n";

    // Overall summary
    if (analysisResult.analysis_summary) {
        copyText += "OVERALL RECOMMENDATION: " + (analysisResult.analysis_summary.overall_recommendation || 'N/A') + "\n";
        copyText += "CONFIDENCE SCORE: " + (analysisResult.analysis_summary.confidence_score || 'N/A') + "\n";
        copyText += "ANALYSIS DATE: " + new Date().toLocaleString() + "\n\n";

        if (analysisResult.analysis_summary.key_findings && analysisResult.analysis_summary.key_findings.length > 0) {
            copyText += "KEY FINDINGS:\n";
            analysisResult.analysis_summary.key_findings.forEach((finding, index) => {
                copyText += `${index + 1}. ${finding}\n`;
            });
            copyText += "\n";
        }
    }

    // Criteria evaluation
    if (analysisResult.criteria_evaluation && analysisResult.criteria_evaluation.length > 0) {
        copyText += "CRITERIA EVALUATION:\n";
        copyText += "===================\n\n";

        analysisResult.criteria_evaluation.forEach((evaluation, index) => {
            copyText += `CRITERION ${index + 1}:\n`;
            copyText += `Result: ${evaluation.evaluation_result || 'N/A'}\n`;
            copyText += `Analysis: ${evaluation.detailed_analysis || 'No detailed analysis provided'}\n`;

            if (evaluation.evidence_found && evaluation.evidence_found.length > 0) {
                copyText += `Evidence Found:\n`;
                evaluation.evidence_found.forEach((evidence, evidenceIndex) => {
                    copyText += `  - ${evidence}\n`;
                });
            } else {
                copyText += `Evidence Found: No specific evidence found\n`;
            }

            copyText += `Confidence: ${evaluation.confidence_level || 'Medium'}\n\n`;
        });
    }

    copyText += "Note: This AI analysis is for reference only. Please review and make your own professional judgment.\n";

    return copyText;
}

// Copy AI results to clipboard
function copyAIResults() {
    if (window.aiResultsCopyText) {
        if (navigator.clipboard && window.isSecureContext) {
            // Use modern clipboard API
            navigator.clipboard.writeText(window.aiResultsCopyText).then(function() {
                // Show success message
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
                btn.classList.remove('btn-light');
                btn.classList.add('btn-success');

                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-light');
                }, 2000);
            }).catch(function(err) {
                console.error('Failed to copy text: ', err);
                fallbackCopyAIResults(window.aiResultsCopyText);
            });
        } else {
            // Fallback for older browsers or non-secure contexts
            fallbackCopyAIResults(window.aiResultsCopyText);
        }
    } else {
        alert('No AI analysis results available to copy.');
    }
}

// Fallback copy method for AI results
function fallbackCopyAIResults(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        btn.classList.remove('btn-light');
        btn.classList.add('btn-success');

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-light');
        }, 2000);
    } catch (err) {
        console.error('Fallback copy failed: ', err);
        alert('Failed to copy text. Please select and copy manually.');
    }

    document.body.removeChild(textArea);
}

// Simple function to populate form with AI results
function populateFormWithAIResults(analysisResult) {
    // Check if there's an error in the analysis result
    if (analysisResult.error) {
        console.error('AI Analysis Error:', analysisResult.error);
        console.error('Parse Error:', analysisResult.parse_error);

        // Try to extract criteria evaluation from raw response if available
        if (analysisResult.raw_response) {
            try {
                // Try to extract JSON from raw response manually
                const cleanedResponse = analysisResult.raw_response
                    .replace(/```json\s*/g, '')
                    .replace(/```\s*/g, '')
                    .trim();

                const parsedResponse = JSON.parse(cleanedResponse);
                console.log('Successfully parsed raw response:', parsedResponse);

                // Recursively call this function with the parsed response
                populateFormWithAIResults(parsedResponse);
                return;
            } catch (e) {
                console.error('Failed to parse raw response:', e);
                // Continue with error handling below
            }
        }

        // If we can't parse the response, just store the error in criteria results
        const criteriaResultsField = document.getElementById('criteriaResultsData');
        if (criteriaResultsField) {
            const errorData = {
                analysis_date: new Date().toISOString(),
                criteria_evaluations: [],
                ai_analysis: {
                    error: true,
                    error_data: analysisResult,
                    analysis_timestamp: new Date().toISOString()
                }
            };
            criteriaResultsField.value = JSON.stringify(errorData);
        }
        return;
    }

    // AI results are now displayed separately - no auto-population of criteria buttons
    // Users will manually review AI analysis and make their own pass/fail decisions

    // Store AI analysis data for reference only (don't auto-populate form)
    window.latestAIAnalysis = {
        ai_analysis: {
            full_analysis_result: analysisResult,
            analysis_timestamp: new Date().toISOString(),
            analysis_method: 'chunked_pdf_analysis'
        }
    };

    console.log('AI analysis stored for reference only:', window.latestAIAnalysis);

    // AI results are now displayed separately - no auto-population of criteria buttons
    // Users will manually review AI analysis and make their own pass/fail decisions
}

// This function has been removed - using simplified displayAIAnalysisResults instead
function displayConsolidatedAnalysisResults_REMOVED(consolidatedResult, resultsContainer) {
    const processingTime = consolidatedResult.analysis_summary.analysis_completion_time
        ? Math.round((new Date(consolidatedResult.analysis_summary.analysis_completion_time) - new Date(consolidatedResult.analysis_summary.analysis_start_time)) / 1000)
        : 0;

    let resultsHTML = `
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>Automatic AI Analysis Complete
                    <span class="badge bg-light text-dark ms-2">${processingTime}s</span>
                </h6>
            </div>
            <div class="card-body">
                <!-- Analysis Summary -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2">Analysis Summary</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h4 class="text-primary mb-1">${consolidatedResult.analysis_summary.total_pages_analyzed}</h4>
                                    <small class="text-muted">Pages Analyzed</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h4 class="text-info mb-1">${consolidatedResult.document_summary.files_analyzed.length}</h4>
                                    <small class="text-muted">Files Processed</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h4 class="text-success mb-1">${processingTime}s</h4>
                                    <small class="text-muted">Processing Time</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 bg-light rounded">
                                    <h4 class="text-warning mb-1">Auto</h4>
                                    <small class="text-muted">Analysis Mode</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Overall Assessment -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2">Overall Assessment Summary</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-success text-white rounded">
                                    <h4 class="mb-1">${Object.values(consolidatedResult.criteria_evaluations).filter(c => c.status === 'Pass').length}</h4>
                                    <small>Criteria Passed</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-danger text-white rounded">
                                    <h4 class="mb-1">${Object.values(consolidatedResult.criteria_evaluations).filter(c => c.status === 'Fail').length}</h4>
                                    <small>Criteria Failed</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 bg-secondary text-white rounded">
                                    <h4 class="mb-1">${Object.values(consolidatedResult.criteria_evaluations).filter(c => c.status === 'Not Evaluated').length}</h4>
                                    <small>Not Evaluated</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Criteria Evaluations -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2">Pre-Screening Criteria Evaluation</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="8%">Criteria</th>
                                        <th width="35%">Description</th>
                                        <th width="12%">Status</th>
                                        <th width="35%">Justification</th>
                                        <th width="10%">Evidence Pages</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    // Add criteria evaluation rows
    Object.keys(consolidatedResult.criteria_evaluations).forEach(criteriaNum => {
        const criteria = consolidatedResult.criteria_evaluations[criteriaNum];
        const statusClass = criteria.status === 'Pass' ? 'success' :
                           criteria.status === 'Fail' ? 'danger' : 'secondary';

        const evidencePages = criteria.evidence_pages.length > 0 ?
            [...new Set(criteria.evidence_pages)].sort((a, b) => a - b).join(', ') :
            'None';

        resultsHTML += `
                                    <tr>
                                        <td class="text-center"><strong>${criteriaNum}</strong></td>
                                        <td>${criteria.criteria_description}</td>
                                        <td class="text-center">
                                            <span class="badge bg-${statusClass}">${criteria.status}</span>
                                        </td>
                                        <td><small>${criteria.justification}</small></td>
                                        <td class="text-center">
                                            <span class="badge bg-light text-dark">${evidencePages}</span>
                                        </td>
                                    </tr>
        `;
    });

    resultsHTML += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Document Summary -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="text-primary border-bottom pb-2">Document Analysis Summary</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-secondary">Files Analyzed:</h6>
                                <ul class="list-unstyled">
                                    ${consolidatedResult.document_summary.files_analyzed.map(file =>
                                        `<li><i class="fas fa-file-pdf text-danger me-2"></i>${file.file_title} (${file.total_pages} pages)</li>`
                                    ).join('')}
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-secondary">Document Types Identified:</h6>
                                <ul class="list-unstyled">
                                    ${[...new Set(consolidatedResult.document_summary.document_types_identified)].map(type =>
                                        `<li><i class="fas fa-tag text-info me-2"></i>${type}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    resultsContainer.innerHTML = resultsHTML;
}

// NEW: Populate criteria evaluation with AI results
function populateCriteriaWithAIResults(consolidatedAnalysis) {
    if (!consolidatedAnalysis || !consolidatedAnalysis.criteria_evaluations) {
        console.log('No AI criteria evaluations found');
        return;
    }

    // Iterate through each criterion and populate with AI results
    Object.keys(consolidatedAnalysis.criteria_evaluations).forEach(criteriaNum => {
        const aiResult = consolidatedAnalysis.criteria_evaluations[criteriaNum];
        const criteriaIndex = parseInt(criteriaNum) - 1; // Convert to 0-based index

        // Update AI analysis display for this criterion
        updateCriteriaAIDisplay(criteriaIndex, aiResult);

        // Automatically set Pass/Fail based on AI analysis
        if (aiResult.status === 'Pass' || aiResult.status === 'Fail') {
            const status = aiResult.status.toLowerCase();
            markCriteria(criteriaIndex, status, true); // true indicates AI-driven
        }
    });

    console.log('AI results populated for criteria evaluation');
}

// NEW: Update AI analysis display for a specific criterion
function updateCriteriaAIDisplay(criteriaIndex, aiResult) {
    console.log(`Updating AI display for criteria ${criteriaIndex}:`, aiResult);

    const aiAnalysisDiv = document.getElementById(`aiAnalysis_${criteriaIndex}`);
    const aiStatusSpan = document.getElementById(`aiStatus_${criteriaIndex}`);
    const aiJustificationDiv = document.getElementById(`aiJustification_${criteriaIndex}`);
    const aiEvidenceDiv = document.getElementById(`aiEvidence_${criteriaIndex}`);
    const aiEvidencePagesSpan = document.getElementById(`aiEvidencePages_${criteriaIndex}`);

    if (!aiAnalysisDiv) {
        console.error(`AI analysis div not found for criteria ${criteriaIndex}`);
        return;
    }

    // Update status badge
    if (aiStatusSpan) {
        const statusClass = aiResult.status === 'Pass' ? 'success' :
                           aiResult.status === 'Fail' ? 'danger' : 'secondary';
        aiStatusSpan.innerHTML = `<span class="badge bg-${statusClass}">${aiResult.status}</span>`;
        console.log(`Updated status for criteria ${criteriaIndex}: ${aiResult.status}`);
    }

    // Update justification
    if (aiJustificationDiv) {
        const justificationText = aiResult.justification || 'No specific justification provided.';
        aiJustificationDiv.innerHTML = `<small>${justificationText}</small>`;
        console.log(`Updated justification for criteria ${criteriaIndex}`);
    }

    // Update evidence - handle both array and string formats
    if (aiEvidenceDiv && aiEvidencePagesSpan) {
        let evidenceText = '';
        if (aiResult.evidence_text && Array.isArray(aiResult.evidence_text) && aiResult.evidence_text.length > 0) {
            evidenceText = aiResult.evidence_text.join(', ');
        } else if (aiResult.evidence_pages && Array.isArray(aiResult.evidence_pages) && aiResult.evidence_pages.length > 0) {
            evidenceText = aiResult.evidence_pages.join(', ');
        }

        if (evidenceText) {
            aiEvidencePagesSpan.textContent = evidenceText;
            aiEvidenceDiv.style.display = 'block';
            console.log(`Updated evidence for criteria ${criteriaIndex}: ${evidenceText}`);
        } else {
            aiEvidenceDiv.style.display = 'none';
            console.log(`No evidence found for criteria ${criteriaIndex}`);
        }
    }
}

// Toggle AI analysis display for a criterion - shows saved data from database
function toggleAIAnalysis(criteriaIndex) {
    const aiAnalysisDiv = document.getElementById(`aiAnalysis_${criteriaIndex}`);
    const toggleText = document.getElementById(`toggleText_${criteriaIndex}`);

    if (!aiAnalysisDiv) return;

    if (aiAnalysisDiv.style.display === 'none' || !aiAnalysisDiv.style.display) {
        // Check if we have saved AI analysis data for this criterion
        const hasSavedData = checkForSavedAIAnalysis(criteriaIndex);

        if (hasSavedData) {
            // Show the saved analysis data
            aiAnalysisDiv.style.display = 'block';
            if (toggleText) toggleText.textContent = 'Hide AI Analysis';
        } else {
            // No saved data available - show message
            showNoAnalysisMessage(criteriaIndex);
        }
    } else {
        aiAnalysisDiv.style.display = 'none';
        if (toggleText) toggleText.textContent = 'Show AI Analysis';
    }
}

// Check if there's saved AI analysis data for a specific criterion
function checkForSavedAIAnalysis(criteriaIndex) {
    const aiStatusSpan = document.getElementById(`aiStatus_${criteriaIndex}`);
    const aiJustificationDiv = document.getElementById(`aiJustification_${criteriaIndex}`);

    // Check if the AI analysis elements have actual data (not just placeholder text)
    if (aiStatusSpan && aiJustificationDiv) {
        const statusText = aiStatusSpan.textContent.trim();
        const justificationText = aiJustificationDiv.textContent.trim();

        // If we have meaningful content (not just "Analyzing..." or "AI analysis in progress...")
        return statusText !== 'Analyzing...' &&
               justificationText !== 'AI analysis in progress...' &&
               statusText !== '' &&
               justificationText !== '';
    }

    return false;
}

// Show message when no AI analysis data is available
function showNoAnalysisMessage(criteriaIndex) {
    const aiAnalysisDiv = document.getElementById(`aiAnalysis_${criteriaIndex}`);
    const toggleText = document.getElementById(`toggleText_${criteriaIndex}`);

    if (aiAnalysisDiv) {
        // Update the content to show a helpful message
        aiAnalysisDiv.innerHTML = `
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark py-2">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>No AI Analysis Available
                    </h6>
                </div>
                <div class="card-body py-3">
                    <p class="mb-2">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        No AI analysis data has been saved for this criterion yet.
                    </p>
                    <p class="mb-0 text-muted">
                        <small>
                            To generate AI analysis, click the main "Analyze with AI" button above.
                            The analysis will be saved and displayed here for each criterion.
                        </small>
                    </p>
                </div>
            </div>
        `;

        aiAnalysisDiv.style.display = 'block';
        if (toggleText) toggleText.textContent = 'Hide AI Analysis';
    }
}

// Populate hidden fields with automatic analysis data
function populateHiddenFieldsFromAutoAnalysis(consolidatedResult) {
    // AI analysis data is now included in the criteria results field
    // No separate field needed

    // Populate individual criteria fields
    Object.keys(consolidatedResult.criteria_evaluations).forEach(criteriaNum => {
        const criteria = consolidatedResult.criteria_evaluations[criteriaNum];

        // Set status based on pass/fail
        const statusField = document.getElementById(`criteria_${criteriaNum}_status`);
        if (statusField) {
            if (criteria.status === 'Pass') {
                statusField.value = 'met';
            } else if (criteria.status === 'Fail') {
                statusField.value = 'not_met';
            } else {
                statusField.value = 'not_evaluated';
            }
        }

        // Set remarks with justification and evidence pages
        const remarksField = document.getElementById(`criteria_${criteriaNum}_remarks`);
        if (remarksField) {
            let remarks = criteria.justification;
            if (criteria.evidence_pages.length > 0) {
                remarks += ` (Evidence found on pages: ${criteria.evidence_pages.join(', ')})`;
            }
            remarksField.value = remarks;
        }
    });

    console.log('Hidden fields populated with automatic analysis data');
}

// Load all PDF files with native PDF viewer
function loadAllPDFsNative() {
    const pdfViewer = document.getElementById('pdfViewer');
    const files = <?= json_encode($applicant_files) ?>;

    if (!pdfViewer) {
        console.error('PDF viewer element not found');
        return;
    }

    if (files.length === 0) {
        pdfViewer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle"></i> No PDF files to display.</div>';
        return;
    }

    // Create native PDF viewers for each file
    let pdfViewersHTML = '';

    files.forEach((file, index) => {
        const filePath = '<?= base_url() ?>' + file.file_path;
        pdfViewersHTML += `
            <div class="pdf-file-container mb-4">
                <div class="pdf-file-header bg-light p-3 rounded-top border d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-file-pdf text-danger me-2"></i>
                        ${file.file_title || file.original_filename || 'PDF Document'}
                        ${file.file_description ? `<small class="text-muted ms-2">(${file.file_description})</small>` : ''}
                    </h6>
                    <div class="btn-group btn-group-sm">
                        <a href="${filePath}" target="_blank" class="btn btn-outline-secondary" title="Open in new tab">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
                <div class="pdf-viewer-wrapper border-start border-end border-bottom rounded-bottom">
                    <iframe
                        src="${filePath}"
                        width="100%"
                        height="600px"
                        style="border: none; display: block;"
                        title="PDF Viewer for ${file.file_title || file.original_filename}">
                        <p>Your browser does not support PDFs.
                           <a href="${filePath}" target="_blank">Download the PDF</a> to view it.
                        </p>
                    </iframe>
                </div>
            </div>
        `;
    });

    pdfViewer.innerHTML = pdfViewersHTML;

    // Initialize AI analysis button for main analysis
    showAIAnalysisButton();
}

// Legacy function - Load and convert all PDF files to images with lazy loading
async function loadAllPDFs() {
    const pdfViewer = document.getElementById('pdfViewer');
    const files = <?= json_encode($applicant_files) ?>;

    if (!pdfViewer) {
        console.error('PDF viewer element not found');
        return;
    }

    if (files.length === 0) {
        pdfViewer.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle"></i> No PDF files to display.</div>';
        return;
    }

    try {
        let totalPages = 0;
        let processedPages = 0;

        // First, count total pages for progress tracking
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const filePath = '<?= base_url() ?>' + file.file_path;

            try {
                const pdf = await pdfjsLib.getDocument(filePath).promise;
                totalPages += pdf.numPages;
            } catch (error) {
                console.error('Error counting pages for:', file.file_title, error);
            }
        }

        // Initialize the viewer with progress indicator and container for pages
        pdfViewer.innerHTML = `
            <div id="conversionProgress" class="text-center p-4 mb-4">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <p class="mt-2">Converting PDF files to images...</p>
                <div class="progress mb-2" style="height: 20px;">
                    <div id="progressBar" class="progress-bar bg-red" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        0%
                    </div>
                </div>
                <p class="text-muted">Processing <span id="currentPage">0</span> of ${totalPages} pages from ${files.length} file(s)</p>
            </div>
            <div id="pdfPagesContainer">
                <!-- Converted pages will appear here as they complete -->
            </div>
        `;

        const progressBar = document.getElementById('progressBar');
        const currentPageSpan = document.getElementById('currentPage');
        const pdfPagesContainer = document.getElementById('pdfPagesContainer');

        // Process each file and convert pages to images with lazy display
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const filePath = '<?= base_url() ?>' + file.file_path;

            // Add file header immediately
            const fileHeaderHtml = `
                <div class="pdf-file-header bg-red text-white p-2 mb-3 rounded" id="fileHeader${i}">
                    <h6 class="mb-0">
                        <i class="fas fa-file-pdf"></i> ${file.file_title || 'Unknown File'}
                        ${file.file_description ? `<small class="ms-2">(${file.file_description})</small>` : ''}
                        <span class="badge bg-light text-dark ms-2">Converting...</span>
                    </h6>
                </div>
                <div id="filePages${i}">
                    <!-- Pages for this file will appear here -->
                </div>
            `;
            pdfPagesContainer.insertAdjacentHTML('beforeend', fileHeaderHtml);

            try {
                const pdf = await pdfjsLib.getDocument(filePath).promise;
                const filePagesContainer = document.getElementById(`filePages${i}`);
                const fileHeader = document.getElementById(`fileHeader${i}`);

                // Initialize conversion tracking if not done yet
                if (totalFilesToConvert === 0) {
                    initializeConversionTracking();
                }

                // Convert each page to image and display immediately
                for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
                    // Update progress
                    processedPages++;
                    const progress = Math.round((processedPages / totalPages) * 100);

                    // Update progress bar
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    progressBar.textContent = `${progress}%`;
                    currentPageSpan.textContent = processedPages;

                    // Create placeholder for this page
                    const pageId = `page${i}_${pageNum}`;
                    const pagePlaceholder = `
                        <div id="${pageId}" class="pdf-page-image mb-4 text-center">
                            <div class="page-info bg-light p-2 mb-2 rounded d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <strong>${file.file_title}</strong> - Page ${pageNum} of ${pdf.numPages}
                                </small>
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">Converting...</span>
                                </div>
                            </div>
                            <div class="text-center p-4 bg-light rounded">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="mt-2 text-muted">Converting page ${pageNum}...</p>
                            </div>
                        </div>
                    `;
                    filePagesContainer.insertAdjacentHTML('beforeend', pagePlaceholder);

                    // Convert page to image
                    const page = await pdf.getPage(pageNum);
                    const viewport = page.getViewport({ scale: 1.5 }); // Higher scale for better quality

                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert canvas to high-quality image
                    const imageDataUrl = canvas.toDataURL('image/png', 1.0);

                    // Replace placeholder with actual image
                    const pageElement = document.getElementById(pageId);
                    if (pageElement) {
                        pageElement.innerHTML = `
                            <div class="page-info bg-light p-2 mb-2 rounded d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <strong>${file.file_title}</strong> - Page ${pageNum} of ${pdf.numPages}
                                </small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'in')" title="Zoom In">
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'out')" title="Zoom Out">
                                        <i class="fas fa-search-minus"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="zoomImage(this, 'reset')" title="Reset Zoom">
                                        <i class="fas fa-expand-arrows-alt"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="image-container" style="overflow: auto; max-height: 800px;">
                                <img src="${imageDataUrl}"
                                     class="pdf-page-img img-fluid border rounded shadow-sm"
                                     style="max-width: 100%; cursor: zoom-in;"
                                     onclick="toggleFullscreen(this)"
                                     alt="Page ${pageNum} of ${file.file_title}" />
                            </div>
                        `;

                        // Add fade-in animation
                        pageElement.style.opacity = '0';
                        pageElement.style.transition = 'opacity 0.3s ease-in-out';
                        setTimeout(() => {
                            pageElement.style.opacity = '1';
                        }, 50);

                        // Store image data for AI analysis
                        if (!convertedImages.find(f => f.fileName === file.file_title)) {
                            convertedImages.push({
                                fileName: file.file_title,
                                images: []
                            });
                        }
                        const fileData = convertedImages.find(f => f.fileName === file.file_title);
                        fileData.images.push(imageDataUrl.split(',')[1]); // Store base64 data only
                    }
                }

                // Update file header to show completion
                if (fileHeader) {
                    const badge = fileHeader.querySelector('.badge');
                    if (badge) {
                        badge.className = 'badge bg-success ms-2';
                        badge.textContent = 'Converted';
                    }
                }

                // Notify that this file is converted (only once per file)
                const fileData = convertedImages.find(f => f.fileName === file.file_title);
                if (fileData) {
                    onFileConverted(fileData);
                }

            } catch (error) {
                console.error('Error converting PDF to images:', file.file_title, error);
                const filePagesContainer = document.getElementById(`filePages${i}`);
                if (filePagesContainer) {
                    filePagesContainer.innerHTML = `
                        <div class="alert alert-warning mb-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            Could not convert PDF to images: ${file.file_title || 'Unknown File'}
                            <br><small>Error: ${error.message}</small>
                        </div>
                    `;
                }

                // Update file header to show error
                const fileHeader = document.getElementById(`fileHeader${i}`);
                if (fileHeader) {
                    const badge = fileHeader.querySelector('.badge');
                    if (badge) {
                        badge.className = 'badge bg-danger ms-2';
                        badge.textContent = 'Error';
                    }
                }
            }
        }

        // Hide progress indicator and show completion message
        const progressDiv = document.getElementById('conversionProgress');
        if (progressDiv) {
            progressDiv.innerHTML = `
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle"></i>
                    Successfully converted ${totalPages} pages from ${files.length} PDF file(s) to images.
                    <button type="button" class="btn btn-sm btn-outline-success ms-2" onclick="this.parentElement.style.display='none'">
                        <i class="fas fa-times"></i> Dismiss
                    </button>
                </div>
            `;
        }

    } catch (error) {
        console.error('Error converting PDFs to images:', error);
        if (pdfViewer) {
            pdfViewer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    Error converting PDF files to images: ${error.message}
                </div>
            `;
        }
    }
}

// Mark criteria as pass/fail
function markCriteria(index, result, isAIDriven = false) {
    criteriaResults[index] = result;

    // Update button states
    const passBtn = document.getElementById(`passBtn_${index}`);
    const failBtn = document.getElementById(`failBtn_${index}`);

    if (!passBtn || !failBtn) return;

    // Reset button states
    passBtn.classList.remove('active', 'btn-success');
    passBtn.classList.add('btn-outline-success');
    failBtn.classList.remove('active', 'btn-danger');
    failBtn.classList.add('btn-outline-danger');

    // Set active state based on result
    if (result === 'pass') {
        passBtn.classList.add('active', 'btn-success');
        passBtn.classList.remove('btn-outline-success');

        // Add AI indicator if this was AI-driven
        if (isAIDriven) {
            passBtn.innerHTML = '<i class="fas fa-check"></i> Pass <small>(AI)</small>';
        }
    } else if (result === 'fail') {
        failBtn.classList.add('active', 'btn-danger');
        failBtn.classList.remove('btn-outline-danger');

        // Add AI indicator if this was AI-driven
        if (isAIDriven) {
            failBtn.innerHTML = '<i class="fas fa-times"></i> Fail <small>(AI)</small>';
        }
    }

    updateCriteriaSummary();
    updateFormCriteriaData();
}

// Update criteria summary
function updateCriteriaSummary() {
    const passed = Object.values(criteriaResults).filter(r => r === 'pass').length;
    const failed = Object.values(criteriaResults).filter(r => r === 'fail').length;
    const pending = totalCriteria - passed - failed;

    const passedElement = document.getElementById('passedCount');
    const failedElement = document.getElementById('failedCount');
    const pendingElement = document.getElementById('pendingCount');
    const progressElement = document.getElementById('progressBar');

    if (passedElement) passedElement.textContent = passed;
    if (failedElement) failedElement.textContent = failed;
    if (pendingElement) pendingElement.textContent = pending;

    const progress = totalCriteria > 0 ? ((passed + failed) / totalCriteria) * 100 : 0;
    if (progressElement) progressElement.style.width = progress + '%';
}

// Update form criteria data for submission
function updateFormCriteriaData() {
    const criteriaResultsField = document.getElementById('criteriaResultsData');
    if (criteriaResultsField) {
        const criteriaData = {
            analysis_date: new Date().toISOString(),
            criteria_evaluations: [],
            overall_summary: {
                total_criteria: totalCriteria,
                passed: Object.values(criteriaResults).filter(r => r === 'pass').length,
                failed: Object.values(criteriaResults).filter(r => r === 'fail').length,
                pending: totalCriteria - Object.values(criteriaResults).filter(r => r === 'pass').length - Object.values(criteriaResults).filter(r => r === 'fail').length
            }
        };

        // Add each criteria result
        preScreenCriteria.forEach((criteria, index) => {
            const result = criteriaResults[index];
            if (result) {
                criteriaData.criteria_evaluations.push({
                    criteria_index: index,
                    criteria_name: criteria.name || `Criteria ${index + 1}`,
                    criteria_description: criteria.description || '',
                    evaluation_result: result === 'pass' ? 'MEETS' : 'DOES NOT MEET',
                    manual_override: true,
                    timestamp: new Date().toISOString()
                });
            }
        });

        criteriaResultsField.value = JSON.stringify(criteriaData);
    }
}

// Image zoom functionality
function zoomImage(button, action) {
    const imageContainer = button.closest('.pdf-page-image').querySelector('.image-container');
    const image = imageContainer.querySelector('.pdf-page-img');

    let currentScale = parseFloat(image.dataset.scale || '1');

    switch(action) {
        case 'in':
            currentScale = Math.min(currentScale * 1.2, 3); // Max 3x zoom
            break;
        case 'out':
            currentScale = Math.max(currentScale / 1.2, 0.5); // Min 0.5x zoom
            break;
        case 'reset':
            currentScale = 1;
            break;
    }

    image.dataset.scale = currentScale;
    image.style.transform = `scale(${currentScale})`;
    image.style.transformOrigin = 'center';

    // Update cursor based on zoom level
    if (currentScale > 1) {
        image.style.cursor = 'zoom-out';
    } else {
        image.style.cursor = 'zoom-in';
    }
}

// Toggle fullscreen for images
function toggleFullscreen(image) {
    if (document.fullscreenElement) {
        document.exitFullscreen();
    } else {
        const container = image.closest('.pdf-page-image');
        if (container.requestFullscreen) {
            container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
        }
    }
}

// AI File Analysis Function
async function analyzeFile(fileId, fileName, filePath) {
    // Show loading modal
    Swal.fire({
        title: 'Analyzing File with AI',
        html: `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <p>AI is analyzing: <strong>${fileName}</strong></p>
                <p class="text-muted">Using Gemini Flash 2.5 multimodal analysis...</p>
                <p class="text-muted">Reading text, images, tables, and charts...</p>
            </div>
        `,
        allowOutsideClick: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    try {
        // Convert PDF to base64 for multimodal analysis
        const fullFilePath = '<?= base_url() ?>' + filePath;
        const pdfBase64 = await convertPDFToBase64(fullFilePath);

        if (!pdfBase64) {
            throw new Error('Could not convert PDF file for AI analysis');
        }

        // Send to AI for multimodal analysis via backend API
        const analysisResult = await sendToBackendForPdfAnalysis(pdfBase64, fileName);

        // Show results
        Swal.fire({
            title: 'AI Analysis Complete',
            html: `
                <div class="text-start">
                    <h6 class="text-primary mb-3">File: ${fileName}</h6>
                    <div class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">
                        <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${JSON.stringify(analysisResult, null, 2)}</pre>
                    </div>
                </div>
            `,
            width: '80%',
            confirmButtonText: 'Close',
            confirmButtonColor: '#F00F00'
        });

    } catch (error) {
        console.error('Error analyzing file:', error);
        Swal.fire({
            title: 'Analysis Failed',
            text: `Error analyzing file: ${error.message}`,
            icon: 'error',
            confirmButtonColor: '#F00F00'
        });
    }
}

// Convert PDF to base64 for multimodal analysis
async function convertPDFToBase64(pdfUrl) {
    try {
        const response = await fetch(pdfUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }

        const arrayBuffer = await response.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        // Convert to base64
        let binary = '';
        const len = uint8Array.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }

        return btoa(binary);
    } catch (error) {
        console.error('Error converting PDF to base64:', error);
        throw new Error('Failed to convert PDF to base64: ' + error.message);
    }
}

// Send PDF to backend for multimodal analysis
async function sendToBackendForPdfAnalysis(pdfBase64, fileName) {
    try {
        const response = await fetch('<?= base_url('api/prescreening/analyze-pdf') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pdf_base64: pdfBase64,
                file_name: fileName
            })
        });

        if (!response.ok) {
            throw new Error(`Backend API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Backend PDF analysis failed');
        }

        return data.analysis;

    } catch (error) {
        console.error('Backend PDF analysis error:', error);
        throw error;
    }
}

// Legacy function - replaced with backend API
async function sendToGeminiMultimodal(pdfBase64, fileName) {
    const prompt = `
Please analyze this PDF document comprehensively using your multimodal capabilities. Read and extract information from:
- All text content
- Images, photos, and graphics
- Tables and charts
- Diagrams and visual elements
- Layout and formatting

DOCUMENT: ${fileName}

Provide a structured JSON response with the applicant's complete profile information:

{
    "document_type": "CV/Resume/Cover Letter/Certificate/Transcript/Portfolio/Other",
    "document_analysis": {
        "total_pages": 0,
        "has_images": false,
        "has_tables": false,
        "has_charts": false,
        "layout_quality": "professional/basic/poor"
    },
    "personal_information": {
        "name": "",
        "email": "",
        "phone": "",
        "address": "",
        "date_of_birth": "",
        "nationality": "",
        "linkedin_profile": "",
        "photo_present": false
    },
    "education": [
        {
            "institution": "",
            "degree": "",
            "field_of_study": "",
            "graduation_year": "",
            "grade": "",
            "honors": "",
            "relevant_coursework": []
        }
    ],
    "work_experience": [
        {
            "company": "",
            "position": "",
            "start_date": "",
            "end_date": "",
            "duration": "",
            "responsibilities": [],
            "achievements": [],
            "technologies_used": []
        }
    ],
    "skills": {
        "technical_skills": [],
        "soft_skills": [],
        "languages": [],
        "software_proficiency": [],
        "programming_languages": []
    },
    "certifications": [
        {
            "name": "",
            "issuing_organization": "",
            "date_obtained": "",
            "expiry_date": "",
            "credential_id": ""
        }
    ],
    "projects": [
        {
            "name": "",
            "description": "",
            "technologies": [],
            "duration": "",
            "role": ""
        }
    ],
    "achievements_awards": [],
    "publications": [],
    "references": [
        {
            "name": "",
            "position": "",
            "company": "",
            "contact": "",
            "relationship": ""
        }
    ],
    "visual_elements": {
        "charts_data": [],
        "images_description": [],
        "tables_content": []
    },
    "summary": "Comprehensive summary of the candidate's profile",
    "key_strengths": [],
    "areas_of_expertise": [],
    "career_progression": "",
    "overall_assessment": {
        "experience_level": "entry/mid/senior/executive",
        "profile_completeness": "excellent/good/fair/poor",
        "presentation_quality": "excellent/good/fair/poor"
    }
}

Extract ALL information present in the document, including data from images, tables, charts, and visual elements. If a field is not available, use null or an empty array.
`;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            contents: [{
                parts: [
                    {
                        text: prompt
                    },
                    {
                        inline_data: {
                            mime_type: "application/pdf",
                            data: pdfBase64
                        }
                    }
                ]
            }],
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 0.95,
                maxOutputTokens: 4096,
            }
        })
    });

    if (!response.ok) {
        throw new Error(`Gemini API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates.length > 0 && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
        const resultText = data.candidates[0].content.parts[0].text;

        // Try to extract JSON from the response
        const jsonMatch = resultText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch (parseError) {
                return {
                    "error": "Could not parse AI response as JSON",
                    "parse_error": parseError.message,
                    "raw_response": resultText
                };
            }
        } else {
            return {
                "error": "No JSON found in AI response",
                "raw_response": resultText
            };
        }
    } else {
        throw new Error('Invalid response from Gemini API');
    }
}

// Format Date of Birth and Calculate Age
function formatDateOfBirth() {
    const dobElement = document.getElementById('dobDisplay');
    const ageElement = document.getElementById('ageDisplay');

    if (dobElement && dobElement.textContent.trim()) {
        const dobValue = dobElement.textContent.trim();

        // Parse the date (assuming it's in YYYY-MM-DD format from database)
        const dobDate = new Date(dobValue);

        if (!isNaN(dobDate.getTime())) {
            // Format as dd/mm/yyyy
            const day = String(dobDate.getDate()).padStart(2, '0');
            const month = String(dobDate.getMonth() + 1).padStart(2, '0');
            const year = dobDate.getFullYear();
            const formattedDate = `${day}/${month}/${year}`;

            // Calculate age
            const today = new Date();
            let age = today.getFullYear() - dobDate.getFullYear();
            const monthDiff = today.getMonth() - dobDate.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dobDate.getDate())) {
                age--;
            }

            // Update display
            dobElement.textContent = formattedDate;
            ageElement.textContent = `(${age} years old)`;
        }
    }
}

// Display Children in Tabular Format
function displayChildren() {
    const childrenContainer = document.getElementById('childrenDisplay');
    const childrenData = <?= json_encode($firstApp['children'] ?? '') ?>;

    if (childrenContainer && childrenData) {
        try {
            // Try to parse as JSON if it's a string
            let children = [];
            if (typeof childrenData === 'string') {
                if (childrenData.trim() === '' || childrenData.trim() === 'null') {
                    childrenContainer.innerHTML = '<span class="text-muted">No children information provided</span>';
                    return;
                }
                try {
                    children = JSON.parse(childrenData);
                } catch (e) {
                    // If not JSON, treat as plain text
                    childrenContainer.innerHTML = `<span class="text-muted">${childrenData}</span>`;
                    return;
                }
            } else if (Array.isArray(childrenData)) {
                children = childrenData;
            }

            if (children && children.length > 0) {
                let tableHTML = `
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>Name</th>
                                    <th>Date of Birth</th>
                                    <th>Gender</th>
                                    <th>Age</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                children.forEach((child, index) => {
                    let age = '';
                    if (child.dob) {
                        const childDob = new Date(child.dob);
                        if (!isNaN(childDob.getTime())) {
                            const today = new Date();
                            let childAge = today.getFullYear() - childDob.getFullYear();
                            const monthDiff = today.getMonth() - childDob.getMonth();

                            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < childDob.getDate())) {
                                childAge--;
                            }
                            age = `${childAge} years`;

                            // Format child DOB as dd/mm/yyyy
                            const day = String(childDob.getDate()).padStart(2, '0');
                            const month = String(childDob.getMonth() + 1).padStart(2, '0');
                            const year = childDob.getFullYear();
                            child.dob = `${day}/${month}/${year}`;
                        }
                    }

                    tableHTML += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${child.name || 'Not specified'}</td>
                            <td>${child.dob || 'Not specified'}</td>
                            <td>${child.gender || 'Not specified'}</td>
                            <td>${age || 'Not specified'}</td>
                        </tr>
                    `;
                });

                tableHTML += `
                            </tbody>
                        </table>
                    </div>
                `;

                childrenContainer.innerHTML = tableHTML;
            } else {
                childrenContainer.innerHTML = '<span class="text-muted">No children information provided</span>';
            }
        } catch (error) {
            console.error('Error displaying children:', error);
            childrenContainer.innerHTML = '<span class="text-muted">Error displaying children information</span>';
        }
    }
}

// Note: Automatic AI analysis functions removed - replaced with simple manual analysis

// Send PDFs to backend for pre-screening analysis
async function sendToBackendForPrescreeningPDFs(pdfDataArray, preScreenCriteria) {
    try {
        const response = await fetch('<?= base_url('api/prescreening/analyze-pdfs') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pdfs: pdfDataArray,
                criteria: preScreenCriteria
            })
        });

        if (!response.ok) {
            throw new Error(`Backend API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Backend analysis failed');
        }

        return data.analysis;

    } catch (error) {
        console.error('Backend prescreening analysis error:', error);
        throw error;
    }
}

// Legacy function - Send images to backend for pre-screening analysis
async function sendToBackendForPrescreening(imageDataArray, preScreenCriteria) {
    try {
        const response = await fetch('<?= base_url('api/prescreening/analyze') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                images: imageDataArray,
                criteria: preScreenCriteria
            })
        });

        if (!response.ok) {
            throw new Error(`Backend API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Backend analysis failed');
        }

        return data.analysis;

    } catch (error) {
        console.error('Backend prescreening analysis error:', error);
        throw error;
    }
}

// Duplicate function removed - using the main displayAIAnalysisResults function above
function displayAIAnalysisResults_DUPLICATE_REMOVED(analysisResult, preScreenCriteria) {
    const resultsContainer = document.getElementById('aiAnalysisResults');

    if (analysisResult.error) {
        resultsContainer.innerHTML = `
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Analysis Completed with Issues</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>Error:</strong> ${analysisResult.error}
                    </div>
                    <details>
                        <summary>Raw AI Response</summary>
                        <pre class="mt-2 p-3 bg-light rounded" style="white-space: pre-wrap; font-size: 0.9em;">${analysisResult.raw_response || 'No response available'}</pre>
                    </details>
                </div>
            </div>
        `;
        return;
    }

    // Build the results HTML
    let resultsHTML = `
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>AI Analysis Complete</h6>
            </div>
            <div class="card-body">
    `;

    // Analysis Summary
    if (analysisResult.analysis_summary) {
        const summary = analysisResult.analysis_summary;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Analysis Summary</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 text-primary">${summary.total_documents_analyzed || 'N/A'}</div>
                                <small class="text-muted">Documents Analyzed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 ${summary.overall_recommendation === 'RECOMMENDED' ? 'text-success' : summary.overall_recommendation === 'NOT RECOMMENDED' ? 'text-danger' : 'text-warning'}">${summary.overall_recommendation || 'N/A'}</div>
                                <small class="text-muted">Overall Recommendation</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h4 text-info">${summary.confidence_score || 'N/A'}</div>
                                <small class="text-muted">Confidence Score</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="h6 text-secondary">${summary.analysis_date ? new Date(summary.analysis_date).toLocaleString() : 'N/A'}</div>
                                <small class="text-muted">Analysis Date</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Criteria Evaluation
    if (analysisResult.criteria_evaluation && analysisResult.criteria_evaluation.length > 0) {
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Pre-Screening Criteria Evaluation</h6>
                    <div class="accordion" id="criteriaAccordion">
        `;

        analysisResult.criteria_evaluation.forEach((evaluation, index) => {
            const badgeClass = evaluation.evaluation_result === 'MEETS' ? 'bg-success' :
                              evaluation.evaluation_result === 'DOES NOT MEET' ? 'bg-danger' : 'bg-warning';

            resultsHTML += `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading${index}">
                        <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                            <span class="badge ${badgeClass} me-3">${evaluation.evaluation_result || 'N/A'}</span>
                            <strong>${evaluation.criterion_title || 'Criterion ' + (index + 1)}</strong>
                            ${evaluation.score ? `<span class="badge bg-secondary ms-auto me-3">${evaluation.score}/100</span>` : ''}
                        </button>
                    </h2>
                    <div id="collapse${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" data-bs-parent="#criteriaAccordion">
                        <div class="accordion-body">
                            ${evaluation.detailed_analysis ? `<p><strong>Analysis:</strong> ${evaluation.detailed_analysis}</p>` : ''}
                            ${evaluation.evidence_found && evaluation.evidence_found.length > 0 ? `
                                <p><strong>Evidence Found:</strong></p>
                                <ul>
                                    ${evaluation.evidence_found.map(evidence => `<li>${evidence}</li>`).join('')}
                                </ul>
                            ` : ''}
                            ${evaluation.recommendation ? `<p><strong>Recommendation:</strong> ${evaluation.recommendation}</p>` : ''}
                        </div>
                    </div>
                </div>
            `;
        });

        resultsHTML += `
                    </div>
                </div>
            </div>
        `;
    }

    // Applicant Profile Summary
    if (analysisResult.applicant_profile) {
        const profile = analysisResult.applicant_profile;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Extracted Applicant Profile</h6>
                    <div class="row">
        `;

        // Personal Information
        if (profile.personal_information) {
            resultsHTML += `
                <div class="col-md-6">
                    <h6 class="text-secondary">Personal Information</h6>
                    <ul class="list-unstyled">
                        ${profile.personal_information.name ? `<li><strong>Name:</strong> ${profile.personal_information.name}</li>` : ''}
                        ${profile.personal_information.email ? `<li><strong>Email:</strong> ${profile.personal_information.email}</li>` : ''}
                        ${profile.personal_information.phone ? `<li><strong>Phone:</strong> ${profile.personal_information.phone}</li>` : ''}
                        ${profile.personal_information.date_of_birth ? `<li><strong>Date of Birth:</strong> ${profile.personal_information.date_of_birth}</li>` : ''}
                    </ul>
                </div>
            `;
        }

        // Skills and Qualifications
        if (profile.skills_and_qualifications && profile.skills_and_qualifications.length > 0) {
            resultsHTML += `
                <div class="col-md-6">
                    <h6 class="text-secondary">Key Skills & Qualifications</h6>
                    <ul>
                        ${profile.skills_and_qualifications.map(skill => `<li>${skill}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        resultsHTML += `
                    </div>
                </div>
            </div>
        `;
    }

    // Overall Assessment
    if (analysisResult.overall_assessment) {
        const assessment = analysisResult.overall_assessment;
        resultsHTML += `
            <div class="row">
                <div class="col-12">
                    <h6 class="text-primary border-bottom pb-2">Overall Assessment</h6>
                    <div class="row">
        `;

        if (assessment.strengths && assessment.strengths.length > 0) {
            resultsHTML += `
                <div class="col-md-4">
                    <h6 class="text-success">Strengths</h6>
                    <ul class="list-group list-group-flush">
                        ${assessment.strengths.map(strength => `<li class="list-group-item border-0 px-0"><i class="fas fa-check text-success me-2"></i>${strength}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        if (assessment.weaknesses && assessment.weaknesses.length > 0) {
            resultsHTML += `
                <div class="col-md-4">
                    <h6 class="text-danger">Areas for Improvement</h6>
                    <ul class="list-group list-group-flush">
                        ${assessment.weaknesses.map(weakness => `<li class="list-group-item border-0 px-0"><i class="fas fa-exclamation-triangle text-warning me-2"></i>${weakness}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        // Next Steps section removed as requested

        resultsHTML += `
                    </div>
        `;

        if (assessment.final_recommendation) {
            resultsHTML += `
                <div class="mt-3">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Final Recommendation</h6>
                        <p class="mb-0">${assessment.final_recommendation}</p>
                    </div>
                </div>
            `;
        }

        resultsHTML += `
                </div>
            </div>
        `;
    }

    resultsHTML += `
            </div>
        </div>
    `;

    resultsContainer.innerHTML = resultsHTML;
}

// Populate hidden fields with AI analysis data
function populateHiddenFields(analysisResult, preScreenCriteria) {
    // AI analysis data is now included in the criteria results field
    // No separate field needed

    // Create criteria results data
    const criteriaResults = {
        analysis_date: new Date().toISOString(),
        criteria_evaluations: [],
        overall_summary: {
            total_criteria: preScreenCriteria.length,
            overall_recommendation: analysisResult.analysis_summary?.overall_recommendation || 'PENDING',
            confidence_score: analysisResult.analysis_summary?.confidence_score || 'N/A'
        }
    };

    // Process each criterion evaluation
    if (analysisResult.criteria_evaluation) {
        analysisResult.criteria_evaluation.forEach((evaluation, index) => {
            criteriaResults.criteria_evaluations.push({
                criterion_number: index + 1,
                criterion_title: evaluation.criterion_title || preScreenCriteria[index]?.name || `Criteria ${index + 1}`,
                evaluation_result: evaluation.evaluation_result || 'PENDING',
                score: evaluation.score || 0,
                evidence_found: evaluation.evidence_found || [],
                detailed_analysis: evaluation.detailed_analysis || '',
                recommendation: evaluation.recommendation || ''
            });
        });
    }

    // Populate criteria results data
    const criteriaResultsField = document.getElementById('criteriaResultsData');
    if (criteriaResultsField) {
        criteriaResultsField.value = JSON.stringify(criteriaResults);
    }
}

// Handle pre-screening form submission
document.addEventListener('DOMContentLoaded', function() {
    formatDateOfBirth();
    displayChildren();

    // Add form submission handler for standard form submission
    const preScreeningForm = document.getElementById('preScreeningForm');
    if (preScreeningForm) {
        preScreeningForm.addEventListener('submit', function(e) {
            // Validate required fields before submission
            const status = document.getElementById('pre_screened_status').value;
            if (!status) {
                Swal.fire({
                    title: 'Validation Error!',
                    text: 'Please select a pre-screening status before saving.',
                    icon: 'warning',
                    confirmButtonColor: '#F00F00'
                });
                e.preventDefault();
                return false;
            }

            // Show loading state
            const saveBtn = document.getElementById('savePreScreeningBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

            // Allow form to submit normally
            return true;
        });
    }
});

// Diagnostic function to test API connectivity and limits
async function testGeminiAPI() {
    const testPrompt = "Please respond with a simple JSON object containing just: {\"test\": \"success\", \"timestamp\": \"" + new Date().toISOString() + "\"}";

    try {
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: testPrompt }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    maxOutputTokens: 100,
                }
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Gemini API Test Success:', data);
            return { success: true, data };
        } else {
            const errorText = await response.text();
            console.error('Gemini API Test Failed:', response.status, errorText);
            return { success: false, status: response.status, error: errorText };
        }
    } catch (error) {
        console.error('Gemini API Test Error:', error);
        return { success: false, error: error.message };
    }
}

// Individual File AI Analysis Function
async function analyzeIndividualFile(fileId, fileName, filePath) {
    const analyzeBtn = document.getElementById(`analyzeBtn_${fileId}`);
    const showProgressCheckbox = document.getElementById(`showProgress_${fileId}`);
    const showProgress = showProgressCheckbox ? showProgressCheckbox.checked : true;

    // Store original button content
    const originalBtnContent = analyzeBtn.innerHTML;

    // Update button to show loading state
    analyzeBtn.disabled = true;
    analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';

    let progressModal = null;

    // Show progress modal only if user wants to see it
    if (showProgress) {
        progressModal = Swal.fire({
            title: 'Analyzing Individual File',
            html: `
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status"></div>
                    <p>AI is creating detailed content profile for: <strong>${fileName}</strong></p>
                    <p class="text-muted">Using Gemini Flash 2.5 multimodal analysis...</p>
                    <p class="text-muted">Analyzing document structure and content authenticity...</p>
                </div>
            `,
            allowOutsideClick: true,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    try {
        // Test API connectivity first
        console.log('Testing Gemini API connectivity...');
        const apiTest = await testGeminiAPI();
        if (!apiTest.success) {
            throw new Error(`API connectivity test failed: ${apiTest.error || apiTest.status}`);
        }
        console.log('API test successful, proceeding with analysis...');

        // Load and analyze PDF structure first
        const fullFilePath = '<?= base_url() ?>' + filePath;
        const pdf = await pdfjsLib.getDocument(fullFilePath).promise;
        const totalPages = pdf.numPages;

        let profileResult;

        if (totalPages > 20) {
            // Update loading message for large documents only if progress is shown
            if (showProgress && progressModal) {
                Swal.update({
                    html: `
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status"></div>
                            <p>Large document detected: <strong>${totalPages} pages</strong></p>
                            <p>AI is analyzing: <strong>${fileName}</strong></p>
                            <p class="text-muted">Splitting into ${Math.ceil(totalPages / 10)} sets of 10 pages each...</p>
                            <p class="text-muted">This may take several minutes for comprehensive analysis.</p>
                            <div class="progress mt-3">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="analysisProgress"></div>
                            </div>
                            <small class="text-muted">Processing progress</small>
                        </div>
                    `
                });
            }

            // Split document and analyze in chunks
            profileResult = await analyzeDocumentInChunks(pdf, fileName, totalPages, showProgress);
        } else {
            // Standard analysis for smaller documents - convert to images instead of PDF
            const imageData = await convertPDFPagesToImages(pdf, 1, totalPages);
            if (!imageData || imageData.length === 0) {
                throw new Error('Could not convert PDF pages to images for AI analysis');
            }
            profileResult = await sendToBackendForFileProfile(imageData, fileName, 1, 1, totalPages);
        }

        // Close progress modal if it was shown
        if (progressModal && showProgress) {
            Swal.close();
        }

        // Display results in a comprehensive modal
        displayFileProfileResults(profileResult, fileName);

    } catch (error) {
        console.error('Error analyzing individual file:', error);

        // Close progress modal if it was shown
        if (progressModal && showProgress) {
            Swal.close();
        }

        Swal.fire({
            title: 'Analysis Failed',
            text: `Error analyzing file: ${error.message}`,
            icon: 'error',
            confirmButtonColor: '#F00F00'
        });
    } finally {
        // Restore button state
        analyzeBtn.disabled = false;
        analyzeBtn.innerHTML = originalBtnContent;
    }
}

// Analyze document in chunks for large files
async function analyzeDocumentInChunks(pdf, fileName, totalPages, showProgress = true) {
    const chunkSize = 10;
    const totalChunks = Math.ceil(totalPages / chunkSize);
    const chunkResults = [];

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const startPage = chunkIndex * chunkSize + 1;
        const endPage = Math.min((chunkIndex + 1) * chunkSize, totalPages);

        // Update progress only if progress display is enabled
        if (showProgress) {
            const progress = ((chunkIndex + 1) / totalChunks) * 100;
            const progressBar = document.getElementById('analysisProgress');
            if (progressBar) {
                progressBar.style.width = progress + '%';
                progressBar.textContent = `${Math.round(progress)}%`;
            }

            // Update status message
            Swal.update({
                html: `
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" role="status"></div>
                        <p>Analyzing chunk ${chunkIndex + 1} of ${totalChunks}</p>
                        <p>Pages ${startPage}-${endPage} of ${totalPages}</p>
                        <p class="text-muted">File: <strong>${fileName}</strong></p>
                        <div class="progress mt-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: ${progress}%">${Math.round(progress)}%</div>
                        </div>
                        <small class="text-muted">Processing progress</small>
                    </div>
                `
            });
        }

        try {
            // Convert pages for this chunk to images
            const chunkImages = await convertPDFPagesToImages(pdf, startPage, endPage);

            // Calculate total chunk size
            const totalChunkSizeKB = chunkImages.reduce((total, img) => total + (img.size_kb || 0), 0);
            console.log(`Chunk ${chunkIndex + 1} total size: ${totalChunkSizeKB} KB (${chunkImages.length} pages)`);

            // Check if chunk is too large
            if (totalChunkSizeKB > 15000) { // 15MB limit
                throw new Error(`Chunk too large: ${totalChunkSizeKB} KB. Consider reducing page count.`);
            }

            // Analyze this chunk with retry mechanism
            const chunkResult = await analyzeChunkWithRetry(
                chunkImages,
                fileName,
                chunkIndex + 1,
                totalChunks,
                totalPages,
                startPage,
                endPage,
                3 // max retries
            );

            chunkResults.push({
                chunk_number: chunkIndex + 1,
                pages_range: `${startPage}-${endPage}`,
                analysis: chunkResult,
                size_kb: chunkSizeKB
            });

        } catch (error) {
            console.error(`Error analyzing chunk ${chunkIndex + 1}:`, error);
            chunkResults.push({
                chunk_number: chunkIndex + 1,
                pages_range: `${startPage}-${endPage}`,
                error: error.message,
                analysis: null,
                error_type: error.message.includes('400') ? 'API_ERROR' : 'PROCESSING_ERROR'
            });
        }
    }

    // Combine all chunk results into a comprehensive profile
    return combineChunkResults(chunkResults, fileName, totalPages);
}

// Convert PDF pages to images for analysis
async function convertPDFPagesToImages(pdf, startPage, endPage) {
    const images = [];

    // Use moderate scale for better quality without huge file sizes
    const scale = 1.5;

    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        try {
            const page = await pdf.getPage(pageNum);
            const viewport = page.getViewport({ scale: scale });

            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = viewport.width;
            canvas.height = viewport.height;

            await page.render({
                canvasContext: context,
                viewport: viewport
            }).promise;

            // Convert to JPEG with good quality but reasonable size
            const imageData = canvas.toDataURL('image/jpeg', 0.85);
            const base64Data = imageData.split(',')[1];

            // Check image size
            const imageSizeKB = Math.round((base64Data.length * 3) / 4 / 1024);
            console.log(`Page ${pageNum} image size: ${imageSizeKB} KB`);

            images.push({
                page_number: pageNum,
                image_data: base64Data,
                size_kb: imageSizeKB
            });

        } catch (error) {
            console.error(`Error converting page ${pageNum}:`, error);
            images.push({
                page_number: pageNum,
                error: error.message,
                image_data: null
            });
        }
    }

    return images;
}

// Extract specific pages from PDF as base64 (legacy function for chunked analysis)
async function extractPagesAsBase64(pdf, startPage, endPage) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    let combinedImageData = [];

    // Use lower scale for large documents to reduce file size
    const pageCount = endPage - startPage + 1;
    const scale = pageCount > 5 ? 1.0 : 1.2; // Reduce scale for larger chunks

    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const viewport = page.getViewport({ scale: scale });

        canvas.width = viewport.width;
        canvas.height = viewport.height;

        await page.render({
            canvasContext: context,
            viewport: viewport
        }).promise;

        // Convert page to base64 with JPEG compression for smaller size
        const pageImageData = canvas.toDataURL('image/jpeg', 0.8); // 80% quality JPEG
        combinedImageData.push(pageImageData.split(',')[1]); // Remove data:image/jpeg;base64, prefix
    }

    // For chunked analysis, we'll create a combined image or send the most representative page
    // For now, return the first page as representative of the chunk
    return combinedImageData[0];
}

// Combine chunk results into comprehensive profile
function combineChunkResults(chunkResults, fileName, totalPages) {
    const combinedProfile = {
        file_metadata: {
            file_name: fileName,
            analysis_date: new Date().toISOString(),
            document_type: "Multi-part Document",
            total_pages: totalPages,
            analysis_method: "Chunked Analysis",
            total_chunks: chunkResults.length,
            chunks_analyzed: chunkResults.filter(chunk => chunk.analysis && !chunk.error).length
        },
        chunk_analyses: chunkResults,
        combined_insights: {
            personal_information: {},
            professional_profile: {},
            education_details: [],
            work_experience: [],
            skills_competencies: {
                technical_skills: [],
                soft_skills: [],
                certifications: []
            },
            document_quality_assessment: {
                overall_completeness: "High",
                chunk_consistency: "Good",
                information_distribution: "Comprehensive"
            }
        },
        ai_analysis_summary: {
            overall_assessment: `Comprehensive ${totalPages}-page document analyzed in ${chunkResults.length} chunks`,
            analysis_coverage: `${chunkResults.filter(chunk => chunk.analysis && !chunk.error).length}/${chunkResults.length} chunks successfully analyzed`,
            notable_features: [],
            recommended_follow_up: [
                "Review individual chunk analyses for detailed insights",
                "Cross-reference information across chunks for consistency",
                "Focus on chunks with highest information density"
            ]
        }
    };

    // Merge information from successful chunk analyses
    chunkResults.forEach(chunk => {
        if (chunk.analysis && !chunk.error) {
            const analysis = chunk.analysis;

            // Merge personal information (prioritize non-empty values)
            if (analysis.personal_information) {
                Object.keys(analysis.personal_information).forEach(key => {
                    if (analysis.personal_information[key] && !combinedProfile.combined_insights.personal_information[key]) {
                        combinedProfile.combined_insights.personal_information[key] = analysis.personal_information[key];
                    }
                });
            }

            // Merge skills (avoid duplicates)
            if (analysis.skills_competencies) {
                if (analysis.skills_competencies.technical_skills) {
                    analysis.skills_competencies.technical_skills.forEach(skill => {
                        if (!combinedProfile.combined_insights.skills_competencies.technical_skills.includes(skill)) {
                            combinedProfile.combined_insights.skills_competencies.technical_skills.push(skill);
                        }
                    });
                }
                if (analysis.skills_competencies.soft_skills) {
                    analysis.skills_competencies.soft_skills.forEach(skill => {
                        if (!combinedProfile.combined_insights.skills_competencies.soft_skills.includes(skill)) {
                            combinedProfile.combined_insights.skills_competencies.soft_skills.push(skill);
                        }
                    });
                }
            }

            // Collect notable features
            if (analysis.ai_analysis_summary && analysis.ai_analysis_summary.notable_features) {
                combinedProfile.ai_analysis_summary.notable_features.push(
                    ...analysis.ai_analysis_summary.notable_features.map(feature =>
                        `Chunk ${chunk.chunk_number} (${chunk.pages_range}): ${feature}`
                    )
                );
            }
        }
    });

    return combinedProfile;
}

// Analyze chunk with retry mechanism
async function analyzeChunkWithRetry(chunkImages, fileName, chunkNumber, totalChunks, totalPages, startPage, endPage, maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Attempt ${attempt}/${maxRetries} for chunk ${chunkNumber}`);

            // Add delay between retries (exponential backoff)
            if (attempt > 1) {
                const delay = Math.pow(2, attempt - 1) * 1000; // 2s, 4s, 8s...
                console.log(`Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }

            const result = await sendToBackendForFileProfile(
                chunkImages,
                fileName,
                chunkNumber,
                totalChunks,
                totalPages,
                startPage,
                endPage
            );

            console.log(`Chunk ${chunkNumber} analyzed successfully on attempt ${attempt}`);
            return result;

        } catch (error) {
            lastError = error;
            console.error(`Attempt ${attempt} failed for chunk ${chunkNumber}:`, error.message);

            // If it's a 400 error, don't retry (likely a permanent issue)
            if (error.message.includes('400')) {
                console.log(`400 error detected, skipping retries for chunk ${chunkNumber}`);
                break;
            }
        }
    }

    // All retries failed, try fallback analysis
    console.log(`All retries failed for chunk ${chunkNumber}, attempting fallback analysis`);
    return await fallbackChunkAnalysis(fileName, chunkNumber, totalChunks, startPage, endPage, lastError);
}

// Fallback analysis when AI fails
async function fallbackChunkAnalysis(fileName, chunkNumber, totalChunks, startPage, endPage, originalError) {
    return {
        file_metadata: {
            file_name: fileName,
            analysis_date: new Date().toISOString(),
            document_type: "Unknown (Analysis Failed)",
            document_category: "Unknown",
            primary_purpose: "Could not analyze due to technical limitations",
            confidence_level: "Low",
            chunk_info: {
                chunk_number: chunkNumber,
                total_chunks: totalChunks,
                pages_analyzed: `${startPage}-${endPage}`,
                is_partial_analysis: true,
                analysis_status: "Failed"
            }
        },
        document_structure: {
            total_pages: endPage - startPage + 1,
            analysis_method: "Fallback",
            original_error: originalError.message
        },
        ai_analysis_summary: {
            overall_assessment: "Analysis failed due to technical limitations. This may be due to file size, content restrictions, or API limitations.",
            analysis_status: "Failed",
            error_details: originalError.message,
            recommended_follow_up: [
                "Try analyzing smaller page ranges",
                "Check if document contains restricted content",
                "Verify file is not corrupted",
                "Consider manual review of this section"
            ]
        },
        extraction_notes: {
            data_extraction_confidence: "None",
            analysis_failure_reason: originalError.message,
            fallback_used: true
        }
    };
}

// Send images to backend for detailed file profiling
async function sendToBackendForFileProfile(imageData, fileName, chunkNumber = 1, totalChunks = 1, totalPages = 0, startPage = 1, endPage = 0) {
    try {
        const response = await fetch('<?= base_url('api/prescreening/profile-file') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                images: imageData,
                file_name: fileName,
                chunk_number: chunkNumber,
                total_chunks: totalChunks,
                total_pages: totalPages,
                start_page: startPage,
                end_page: endPage
            })
        });

        if (!response.ok) {
            throw new Error(`Backend API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Backend file profile analysis failed');
        }

        return data.analysis;

    } catch (error) {
        console.error('Backend file profile analysis error:', error);
        throw error;
    }
}

// Legacy function - replaced with backend API
async function sendToGeminiForFileProfile(imageData, fileName, chunkNumber = 1, totalChunks = 1, totalPages = 0, startPage = 1, endPage = 0) {
    const chunkInfo = totalChunks > 1 ?
        `\n\nCHUNK ANALYSIS INFO:
- This is chunk ${chunkNumber} of ${totalChunks} total chunks
- Analyzing pages ${startPage}-${endPage} of ${totalPages} total pages
- Focus on extracting information specific to this section
- Note any incomplete information that may continue in other chunks` : '';

    const prompt = `
You are an expert document analyst. Analyze each page of this document and create a page-by-page profile focusing on document type, content summary, and notable features.

DOCUMENT: ${fileName}${chunkInfo}

ANALYSIS APPROACH:
For each page, provide:
1. PAGE TYPE: What type of document/page is this (e.g., CV page 1, certificate, transcript page 2, cover letter, etc.)
2. CONTENT SUMMARY: Brief summary of what information this page contains
3. NOTABLE FEATURES: Visual elements like stamps, signatures, handwritten notes, headers, logos, photos, etc.
4. PAGE RELATIONSHIP: How this page relates to the document (first page, continuation, last page, standalone, etc.)
5. AUTHENTICITY CHECK: Does this page appear to belong to the applicant? Any red flags?

Create a simple JSON structure like this:

{
    "document_profile": {
        "file_name": "${fileName}",
        "analysis_date": "${new Date().toISOString()}",
        "total_pages_analyzed": ${totalChunks > 1 ? `${endPage - startPage + 1}` : 'totalPages'}${totalChunks > 1 ? `,
        "chunk_info": {
            "chunk_number": ${chunkNumber},
            "total_chunks": ${totalChunks},
            "pages_range": "${startPage}-${endPage}"
        }` : ''}
    },
    "page_analysis": [
        {
            "page_number": 1,
            "page_type": "What type of document/page is this (e.g., CV page 1, certificate, transcript page 2, cover letter, etc.)",
            "content_summary": "Brief summary of what information this page contains - don't extract everything, just summarize",
            "notable_features": [
                "List visual elements like: stamps, signatures, handwritten notes, headers, logos, photos, tables, etc."
            ],
            "page_relationship": "How this page relates to the document (first page, continuation, last page, standalone, part of multi-page document, etc.)",
            "authenticity_check": {
                "belongs_to_applicant": "YES/NO/UNCERTAIN",
                "concerns": ["Any red flags or suspicious elements"],
                "confidence": "HIGH/MEDIUM/LOW"
            }
        }
    ],
    "overall_document_assessment": {
        "document_type": "What type of document is this overall",
        "document_purpose": "What is this document's main purpose",
        "authenticity_verdict": "AUTHENTIC/QUESTIONABLE/REQUIRES_VERIFICATION",
        "key_observations": ["Most important observations about this document"],
        "recommended_actions": ["What should be done with this document"]
    }
}

ANALYSIS INSTRUCTIONS:

1. Look at each page individually
2. For each page, identify:
   - What type of page/document it is
   - Summarize the content (don't extract everything)
   - Note visual features (stamps, signatures, logos, etc.)
   - Determine how it relates to the overall document
   - Check if it appears to belong to the applicant

3. Focus on PROFILING the document, not extracting all data
4. Pay attention to authenticity - does this document belong to the applicant?
5. Note any suspicious elements or red flags

Example of what I want:
"Page 1 is the first page of a CV, contains personal information and contact details, has a professional header with the applicant's name, this page appears authentic and belongs to the applicant"

"Page 2 is a continuation of the CV showing work experience section, contains employment history from 2018-2023, has consistent formatting with page 1, this page is a continuation of the CV document"

Keep it simple and focused on document profiling, not data extraction.
`;

    // Prepare request parts with text and images
    const parts = [{ text: prompt }];

    // Add images to the request
    if (Array.isArray(imageData)) {
        // Multiple images (from convertPDFPagesToImages)
        imageData.forEach(imgData => {
            if (imgData.image_data) {
                parts.push({
                    inline_data: {
                        mime_type: "image/jpeg",
                        data: imgData.image_data
                    }
                });
            }
        });
    } else {
        // Single image (legacy support)
        parts.push({
            inline_data: {
                mime_type: "image/jpeg",
                data: imageData
            }
        });
    }

    const requestBody = {
        contents: [{ parts: parts }],
        generationConfig: {
            temperature: 0.1,
            topK: 32,
            topP: 0.95,
            maxOutputTokens: 8192,
        }
    };

    const requestSizeKB = Math.round(JSON.stringify(requestBody).length / 1024);
    console.log(`Gemini API Request - ${Array.isArray(imageData) ? imageData.length : 1} images:`);
    console.log(`- Request size: ${requestSizeKB} KB`);
    console.log(`- Prompt length: ${prompt.length} characters`);

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
        // Get detailed error information
        let errorDetails = '';
        try {
            const errorData = await response.json();
            errorDetails = JSON.stringify(errorData, null, 2);
            console.error('Gemini API Error Details:', errorData);
        } catch (e) {
            errorDetails = await response.text();
            console.error('Gemini API Error Text:', errorDetails);
        }

        throw new Error(`Gemini API request failed: ${response.status} ${response.statusText}\nDetails: ${errorDetails}`);
    }

    const data = await response.json();

    if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts[0]) {
        const resultText = data.candidates[0].content.parts[0].text;

        // Try to extract JSON from the response
        const jsonMatch = resultText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch (parseError) {
                return {
                    "error": "Could not parse AI response as JSON",
                    "parse_error": parseError.message,
                    "raw_response": resultText
                };
            }
        } else {
            return {
                "error": "No JSON found in AI response",
                "raw_response": resultText
            };
        }
    } else {
        throw new Error('Invalid response from Gemini API');
    }
}

// Display File Profile Results
function displayFileProfileResults(profileResult, fileName) {
    if (profileResult.error) {
        Swal.fire({
            title: 'Analysis Completed with Issues',
            html: `
                <div class="text-start">
                    <div class="alert alert-warning">
                        <strong>Error:</strong> ${profileResult.error}
                    </div>
                    <details>
                        <summary>Raw AI Response</summary>
                        <pre class="mt-2 p-3 bg-light rounded" style="white-space: pre-wrap; font-size: 0.9em;">${profileResult.raw_response || 'No response available'}</pre>
                    </details>
                </div>
            `,
            width: '90%',
            confirmButtonText: 'Close',
            confirmButtonColor: '#F00F00'
        });
        return;
    }

    // Build comprehensive results HTML
    let resultsHTML = `
        <div class="text-start">
            <div class="row mb-3">
                <div class="col-12">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-file-alt me-2"></i>File Profile: ${fileName}
                    </h5>
                </div>
            </div>
    `;

    // File Metadata Summary
    if (profileResult.file_metadata) {
        const metadata = profileResult.file_metadata;
        const isChunkedAnalysis = metadata.chunk_info || profileResult.chunk_analyses;

        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Document Overview</h6>
                    ${isChunkedAnalysis ? `
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Large Document Analysis:</strong>
                            ${metadata.total_pages || profileResult.file_metadata?.total_pages || 'Multiple'} pages analyzed in
                            ${metadata.total_chunks || profileResult.chunk_analyses?.length || 'multiple'} chunks for comprehensive coverage.
                        </div>
                    ` : ''}
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-primary">${metadata.document_type || 'Unknown'}</div>
                                <small class="text-muted">Document Type</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-info">${metadata.document_category || 'Unknown'}</div>
                                <small class="text-muted">Category</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-success">${metadata.confidence_level || 'Unknown'}</div>
                                <small class="text-muted">Confidence</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-warning">${metadata.total_pages || profileResult.document_structure?.total_pages || 'N/A'}</div>
                                <small class="text-muted">Total Pages</small>
                            </div>
                        </div>
                    </div>
                    ${metadata.primary_purpose ? `<p class="mt-2 mb-0"><strong>Purpose:</strong> ${metadata.primary_purpose}</p>` : ''}
                </div>
            </div>
        `;
    }

    // Chunked Analysis Summary (if applicable)
    if (profileResult.chunk_analyses) {
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Chunk Analysis Summary</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-success text-white rounded">
                                <div class="h6">${profileResult.chunk_analyses.filter(chunk => chunk.analysis && !chunk.error).length}</div>
                                <small>Successful Analyses</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-warning text-white rounded">
                                <div class="h6">${profileResult.chunk_analyses.filter(chunk => chunk.error).length}</div>
                                <small>Failed Analyses</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-2 bg-info text-white rounded">
                                <div class="h6">${profileResult.chunk_analyses.length}</div>
                                <small>Total Chunks</small>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <details>
                            <summary class="btn btn-outline-primary btn-sm">View Individual Chunk Results</summary>
                            <div class="mt-3">
                                ${profileResult.chunk_analyses.map((chunk, index) => `
                                    <div class="card mb-2">
                                        <div class="card-header py-2">
                                            <h6 class="mb-0">
                                                Chunk ${chunk.chunk_number} - Pages ${chunk.pages_range}
                                                ${chunk.error ? '<span class="badge bg-danger ms-2">Error</span>' : '<span class="badge bg-success ms-2">Success</span>'}
                                            </h6>
                                        </div>
                                        ${chunk.error ? `
                                            <div class="card-body py-2">
                                                <small class="text-danger">Error: ${chunk.error}</small>
                                            </div>
                                        ` : `
                                            <div class="card-body py-2">
                                                <small class="text-muted">
                                                    Document Type: ${chunk.analysis?.file_metadata?.document_type || 'Unknown'} |
                                                    Confidence: ${chunk.analysis?.file_metadata?.confidence_level || 'Unknown'}
                                                </small>
                                            </div>
                                        `}
                                    </div>
                                `).join('')}
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        `;
    }

    // Personal Information (use combined insights for chunked analysis)
    const personalInfo = profileResult.combined_insights?.personal_information || profileResult.personal_information;
    if (personalInfo) {
        const personal = personalInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Personal Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            ${personal.full_name ? `<p><strong>Name:</strong> ${personal.full_name}</p>` : ''}
                            ${personal.professional_title ? `<p><strong>Title:</strong> ${personal.professional_title}</p>` : ''}
                            ${personal.email_address ? `<p><strong>Email:</strong> ${personal.email_address}</p>` : ''}
                            ${personal.phone_number ? `<p><strong>Phone:</strong> ${personal.phone_number}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            ${personal.current_position ? `<p><strong>Position:</strong> ${personal.current_position}</p>` : ''}
                            ${personal.current_employer ? `<p><strong>Employer:</strong> ${personal.current_employer}</p>` : ''}
                            ${personal.nationality ? `<p><strong>Nationality:</strong> ${personal.nationality}</p>` : ''}
                            ${personal.linkedin_profile ? `<p><strong>LinkedIn:</strong> <a href="${personal.linkedin_profile}" target="_blank">${personal.linkedin_profile}</a></p>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Professional Profile (use combined insights for chunked analysis)
    const professionalInfo = profileResult.combined_insights?.professional_profile || profileResult.professional_profile;
    if (professionalInfo) {
        const prof = professionalInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Professional Profile</h6>
                    <div class="row">
                        <div class="col-md-6">
                            ${prof.career_level ? `<p><strong>Career Level:</strong> <span class="badge bg-primary">${prof.career_level}</span></p>` : ''}
                            ${prof.years_of_experience ? `<p><strong>Experience:</strong> ${prof.years_of_experience}</p>` : ''}
                            ${prof.industry_focus && prof.industry_focus.length > 0 ? `<p><strong>Industry Focus:</strong> ${prof.industry_focus.join(', ')}</p>` : ''}
                        </div>
                        <div class="col-md-6">
                            ${prof.specializations && prof.specializations.length > 0 ? `<p><strong>Specializations:</strong> ${prof.specializations.join(', ')}</p>` : ''}
                            ${prof.current_role_summary ? `<p><strong>Current Role:</strong> ${prof.current_role_summary}</p>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Skills & Competencies (use combined insights for chunked analysis)
    const skillsInfo = profileResult.combined_insights?.skills_competencies || profileResult.skills_competencies;
    if (skillsInfo) {
        const skills = skillsInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Skills & Competencies</h6>
                    <div class="row">
        `;

        if (skills.technical_skills && skills.technical_skills.length > 0) {
            resultsHTML += `
                <div class="col-md-6">
                    <strong>Technical Skills:</strong>
                    <div class="mt-1">
                        ${skills.technical_skills.map(skill => `<span class="badge bg-info me-1 mb-1">${skill}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        if (skills.soft_skills && skills.soft_skills.length > 0) {
            resultsHTML += `
                <div class="col-md-6">
                    <strong>Soft Skills:</strong>
                    <div class="mt-1">
                        ${skills.soft_skills.map(skill => `<span class="badge bg-success me-1 mb-1">${skill}</span>`).join('')}
                    </div>
                </div>
            `;
        }

        resultsHTML += `
                    </div>
                </div>
            </div>
        `;
    }

    // Document Quality Assessment (use combined insights for chunked analysis)
    const qualityInfo = profileResult.combined_insights?.document_quality_assessment || profileResult.document_quality_assessment;
    if (qualityInfo) {
        const quality = qualityInfo;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">Document Quality Assessment</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-primary">${quality.completeness_score || 'N/A'}</div>
                                <small class="text-muted">Completeness</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-info">${quality.presentation_quality || 'N/A'}</div>
                                <small class="text-muted">Presentation</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 text-success">${quality.information_clarity || 'N/A'}</div>
                                <small class="text-muted">Clarity</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="h6 ${quality.professional_formatting ? 'text-success' : 'text-warning'}">${quality.professional_formatting ? 'Yes' : 'No'}</div>
                                <small class="text-muted">Professional Format</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // AI Analysis Summary
    if (profileResult.ai_analysis_summary) {
        const summary = profileResult.ai_analysis_summary;
        resultsHTML += `
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-secondary border-bottom pb-2">AI Analysis Summary</h6>
                    ${summary.overall_assessment ? `<p><strong>Overall Assessment:</strong> ${summary.overall_assessment}</p>` : ''}
                    ${summary.document_strength ? `<p><strong>Document Strength:</strong> ${summary.document_strength}</p>` : ''}

                    ${summary.notable_features && summary.notable_features.length > 0 ? `
                        <div class="mb-2">
                            <strong>Notable Features:</strong>
                            <ul class="mt-1">
                                ${summary.notable_features.map(feature => `<li>${feature}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${summary.recommended_follow_up && summary.recommended_follow_up.length > 0 ? `
                        <div class="mb-2">
                            <strong>Recommended Follow-up:</strong>
                            <ul class="mt-1">
                                ${summary.recommended_follow_up.map(item => `<li>${item}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Raw JSON Data (collapsible)
    resultsHTML += `
        <div class="row">
            <div class="col-12">
                <details>
                    <summary class="btn btn-outline-secondary btn-sm">View Complete JSON Profile</summary>
                    <pre class="mt-3 p-3 bg-light rounded" style="white-space: pre-wrap; font-size: 0.85em; max-height: 400px; overflow-y: auto;">${JSON.stringify(profileResult, null, 2)}</pre>
                </details>
            </div>
        </div>
    `;

    resultsHTML += `</div>`;

    // Show results in modal
    Swal.fire({
        title: 'File Profile Analysis Complete',
        html: resultsHTML,
        width: '95%',
        confirmButtonText: 'Close',
        confirmButtonColor: '#F00F00',
        customClass: {
            popup: 'text-start'
        }
    });
}

// Load existing criteria results when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadExistingCriteriaResults();
});

function loadExistingCriteriaResults() {
    // Get existing criteria results from PHP
    const existingCriteriaResults = <?= json_encode($existing_criteria_results ?? []) ?>;
    const existingAiAnalysis = <?= json_encode($existing_ai_analysis ?? []) ?>;

    // AI analysis data is now embedded within the criteria results, so no separate loading needed
    if (existingAiAnalysis && Object.keys(existingAiAnalysis).length > 0) {
        console.log('Loaded existing AI analysis data:', existingAiAnalysis);
    }

    if (existingCriteriaResults && existingCriteriaResults.criteria_evaluations) {
        console.log('Loading existing criteria results:', existingCriteriaResults);

        // Load individual criteria results
        existingCriteriaResults.criteria_evaluations.forEach(evaluation => {
            const criteriaIndex = evaluation.criteria_index;
            const result = evaluation.evaluation_result;

            // Determine if it's pass or fail
            let status = 'pending';
            if (result === 'MEETS' || result === 'Pass' || result === 'pass') {
                status = 'pass';
            } else if (result === 'DOES NOT MEET' || result === 'Fail' || result === 'fail') {
                status = 'fail';
            }

            if (status !== 'pending') {
                // Update the criteria result
                criteriaResults[criteriaIndex] = status;

                // Update the UI buttons
                const passBtn = document.getElementById(`passBtn_${criteriaIndex}`);
                const failBtn = document.getElementById(`failBtn_${criteriaIndex}`);

                if (passBtn && failBtn) {
                    // Reset both buttons
                    passBtn.classList.remove('btn-success', 'active');
                    passBtn.classList.add('btn-outline-success');
                    failBtn.classList.remove('btn-danger', 'active');
                    failBtn.classList.add('btn-outline-danger');

                    // Activate the correct button
                    if (status === 'pass') {
                        passBtn.classList.remove('btn-outline-success');
                        passBtn.classList.add('btn-success', 'active');
                    } else if (status === 'fail') {
                        failBtn.classList.remove('btn-outline-danger');
                        failBtn.classList.add('btn-danger', 'active');
                    }
                }
            }

            // Load AI analysis data if available
            if (evaluation.ai_analysis) {
                const aiResult = {
                    status: status === 'pass' ? 'Pass' : 'Fail',
                    justification: evaluation.ai_analysis.detailed_analysis || evaluation.ai_analysis.justification || 'AI analysis completed.',
                    evidence_pages: evaluation.ai_analysis.evidence_found || [],
                    evidence_text: evaluation.ai_analysis.evidence_found || [],
                    confidence_level: evaluation.ai_analysis.confidence_level || 'Medium'
                };

                // Update the AI analysis display for this criterion
                updateCriteriaAIDisplay(criteriaIndex, aiResult);
                console.log(`Loaded AI analysis for criteria ${criteriaIndex}:`, aiResult);
            }
        });

        // Update the summary and form data
        updateCriteriaSummary();
        updateFormCriteriaData();

        console.log('Loaded existing criteria results:', criteriaResults);
    } else {
        console.log('No existing criteria results found');
    }
}
</script>
<?= $this->endSection() ?>

<?php

namespace App\Controllers;

use App\Models\usersModel;

class ApplicantAuthController extends BaseController
{
    protected $usersModel;
    protected $applicantsModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->usersModel = new usersModel();
        $this->applicantsModel = new \App\Models\applicantsModel();
    }

    public function loginForm()
    {
        return view('home/home_login', ['title' => 'Login', 'menu' => 'login']);
    }

    public function processLogin()
    {
        if (!$this->request->getPost('username') || !$this->request->getPost('password')) {
            return redirect()->back()->with('error', 'Username and password are required');
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        $user = $this->usersModel->where('username', $username)->first();

        if ($user && password_verify($password, $user['password'])) {
            if ($user['status'] != 1) {
                return redirect()->back()->with('error', 'Your account is inactive. Please contact administrator.');
            }

            $this->session->set([
                'logged_in' => true,
                'user_id' => $user['id'],
                'name' => $user['name'],
                'role' => $user['role'],
                'org_id' => $user['org_id']
            ]);

            return redirect()->to('dashboard')->with('success', "Welcome back, {$user['name']}! You've successfully logged in.");
        }

        return redirect()->back()->with('error', 'Invalid username or password. Please try again.');
    }

    public function registerForm()
    {
        return view('home/home_register', ['title' => 'Register', 'menu' => 'register']);
    }

    public function processRegister()
    {
        // Log the incoming request data
        log_message('debug', 'Registration attempt with data: ' . json_encode($this->request->getPost()));

        // Validate form data
        $rules = [
            'firstname' => 'required|min_length[3]|max_length[50]',
            'lastname' => 'required|min_length[3]|max_length[50]',
            'email' => 'required|valid_email|is_unique[applicants.email]',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]'
        ];

        if (!$this->validate($rules)) {
            $errors = $this->validator->getErrors();
            log_message('debug', 'Validation failed with errors: ' . json_encode($errors));
            return redirect()->back()->withInput()->with('errors', $errors);
        }

        // Generate unique ID and activation token
        $unique_id = 'APP' . time() . rand(1000, 9999);
        $activation_token = bin2hex(random_bytes(32));

        // Prepare data for insertion
        $data = [
            'unique_id' => $unique_id,
            'fname' => $this->request->getPost('firstname'),
            'lname' => $this->request->getPost('lastname'),
            'email' => $this->request->getPost('email'),
            'password' => $this->request->getPost('password'), // Model will hash this
            'activation_token' => $activation_token,
            'status' => 0
        ];

        try {
            // Save applicant data
            $inserted = $this->applicantsModel->insert($data);
            log_message('debug', 'Applicant data insertion result: ' . ($inserted ? 'success' : 'failed'));

            if (!$inserted) {
                log_message('error', 'Failed to insert applicant data: ' . json_encode($this->applicantsModel->errors()));
                return redirect()->back()->withInput()->with('error', 'Failed to create account. Please try again.');
            }

            // Send activation email
            $email = \Config\Services::email();

            $emailConfig = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.dakoiims.com',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'dakoiianzii',
                'SMTPPort' => 465,
                'SMTPCrypto' => 'ssl',
                'mailType' => 'html'
            ];

            $email->initialize($emailConfig);
            $email->setFrom('<EMAIL>', 'DERS System');
            $email->setTo($data['email']);
            $email->setSubject('Activate Your DERS Account');

            $activation_link = base_url("applicant/activate/{$activation_token}");
            $email_body = view('emails/emails_activation_email', [
                'firstname' => $data['fname'],
                'activation_link' => $activation_link
            ]);

            $email->setMessage($email_body);

            $emailSent = $email->send();
            log_message('debug', 'Email sending result: ' . ($emailSent ? 'success' : 'failed'));
            if (!$emailSent) {
                log_message('error', 'Email error: ' . $email->printDebugger(['headers']));
            }

            if ($emailSent) {
                session()->setFlashdata('swal_icon', 'success');
                session()->setFlashdata('swal_title', 'Registration Successful!');
                session()->setFlashdata('swal_text', 'Please check your email to activate your account.');
                return redirect()->to('/');
            } else {
                session()->setFlashdata('swal_icon', 'warning');
                session()->setFlashdata('swal_title', 'Account Created');
                session()->setFlashdata('swal_text', 'Account created but activation email could not be sent. Please contact <NAME_EMAIL>.');
                return redirect()->to('/');
            }
        } catch (\Exception $e) {
            log_message('error', 'Registration error: ' . $e->getMessage());
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Registration Failed');
            session()->setFlashdata('swal_text', 'An error occurred during registration. Please try again.');
            return redirect()->back()->withInput();
        }
    }

    public function activate($token)
    {
        $applicant = $this->applicantsModel->where('activation_token', $token)->first();

        if (!$applicant) {
            session()->setFlashdata('swal_icon', 'error');
            session()->setFlashdata('swal_title', 'Invalid Token');
            session()->setFlashdata('swal_text', 'The activation token is invalid or has expired.');
            return redirect()->to('/');
        }

        if ($applicant['status'] == 1) {
            session()->setFlashdata('swal_icon', 'info');
            session()->setFlashdata('swal_title', 'Already Activated');
            session()->setFlashdata('swal_text', 'Your account is already activated. Please login.');
            return redirect()->to('/');
        }

        // Activate the account
        $this->applicantsModel->update($applicant['applicant_id'], [
            'status' => 1,
            'activation_token' => null
        ]);

        session()->setFlashdata('swal_icon', 'success');
        session()->setFlashdata('swal_title', 'Account Activated');
        session()->setFlashdata('swal_text', 'Your account has been activated successfully. You can now login.');
        return redirect()->to('/');
    }

    public function applicantLoginForm()
    {
        return view('home/home_applicant_login', ['title' => 'Applicant Login', 'menu' => 'applicant_login']);
    }

    public function processApplicantLogin()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');

        $applicant = $this->applicantsModel->where('email', $email)->first();

        if ($applicant && password_verify($password, $applicant['password'])) {
            if ($applicant['status'] != 1) {
                session()->setFlashdata('swal_icon', 'warning');
                session()->setFlashdata('swal_title', 'Account Not Activated');
                session()->setFlashdata('swal_text', 'Please activate your account first. Check your email for the activation link.');
                return redirect()->back();
            }

            $this->session->set([
                'logged_in' => true,
                'applicant_id' => $applicant['applicant_id'],
                'applicant_name' => $applicant['fname'] . ' ' . $applicant['lname'],
                'applicant_email' => $applicant['email']
            ]);

            session()->setFlashdata('swal_icon', 'success');
            session()->setFlashdata('swal_title', 'Welcome Back!');
            session()->setFlashdata('swal_text', 'Welcome back, ' . $applicant['fname'] . '!');
            return redirect()->to('applicant/dashboard');
        }

        session()->setFlashdata('swal_icon', 'error');
        session()->setFlashdata('swal_title', 'Login Failed');
        session()->setFlashdata('swal_text', 'Invalid email or password.');
        return redirect()->back();
    }
}

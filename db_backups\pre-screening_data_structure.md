# Pre-Screening Data Structure

## Overview
This document defines the standard JSON structure for pre-screening data in the DERS (Dakoii Echad Recruitment & Selection System). The pre-screening data represents the evaluation results for applicants and is stored in the `appx_application_details` table.

## Database Context
- **Table**: `appx_application_details`
- **Fields**: 
  - `pre_screened_criteria_results` (LONGTEXT) - Criteria evaluation results
  - `pre_screened_ai_analysis` (LONGTEXT) - AI analysis data
  - `pre_screened_status` (VARCHAR) - Overall status (passed/failed)
  - `pre_screened_remarks` (TEXT) - Human reviewer remarks
  - `pre_screened_at` (DATETIME) - Timestamp of evaluation
  - `pre_screened_by` (INT) - User ID of evaluator
- **Storage Format**: JSON string
- **Related Models**: `AppxApplicationDetailsModel.php`
- **Controllers**: `ApplicationPreScreeningController.php`

## Pre-Screened Criteria Results Structure

### Basic Structure
```json
{
  "analysis_date": "2025-08-11T10:30:00.000Z",
  "criteria_evaluations": [
    {
      "criterion_number": 1,
      "criterion_title": "The RS 3.2 must be signed",
      "evaluation_result": "MEETS",
      "score": 85,
      "evidence_found": [
        "Signature found on RS 3.2 form",
        "Document appears to be properly signed"
      ],
      "detailed_analysis": "The applicant has properly signed the RS 3.2 form as required.",
      "recommendation": "Criterion met - proceed with application"
    }
  ],
  "overall_summary": {
    "total_criteria": 4,
    "criteria_passed": 3,
    "criteria_failed": 1,
    "overall_recommendation": "RECOMMENDED",
    "confidence_score": "85%"
  }
}
```

### Field Specifications

#### Root Level Fields
- **analysis_date** (string, ISO 8601): Timestamp when the analysis was performed
- **criteria_evaluations** (array): Array of individual criterion evaluations
- **overall_summary** (object): Summary of the overall evaluation

#### Criteria Evaluation Object
- **criterion_number** (integer): Sequential number of the criterion (1-based)
- **criterion_title** (string): Name/title of the criterion being evaluated
- **evaluation_result** (string): Result of evaluation
  - Values: `"MEETS"`, `"DOES NOT MEET"`, `"PARTIALLY MEETS"`, `"PENDING"`
- **score** (integer): Numerical score (0-100)
- **evidence_found** (array): List of evidence supporting the evaluation
- **detailed_analysis** (string): Detailed explanation of the evaluation
- **recommendation** (string): Specific recommendation for this criterion

#### Overall Summary Object
- **total_criteria** (integer): Total number of criteria evaluated
- **criteria_passed** (integer): Number of criteria that were met
- **criteria_failed** (integer): Number of criteria that were not met
- **overall_recommendation** (string): Final recommendation
  - Values: `"RECOMMENDED"`, `"NOT RECOMMENDED"`, `"REQUIRES REVIEW"`, `"PENDING"`
- **confidence_score** (string): Confidence level of the evaluation (percentage)

## Pre-Screened AI Analysis Structure

### Basic Structure
```json
{
  "analysis_summary": {
    "total_documents_analyzed": 5,
    "total_pages_analyzed": 23,
    "analysis_date": "2025-08-11T10:30:00.000Z",
    "analysis_completion_time": "2025-08-11T10:35:00.000Z",
    "processing_method": "automatic_page_by_page",
    "overall_recommendation": "RECOMMENDED",
    "confidence_score": "85%"
  },
  "applicant_profile": {
    "personal_information": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "address": "123 Main Street, City",
      "date_of_birth": "1990-01-15",
      "nationality": "Country Name"
    },
    "education": [
      {
        "institution": "University Name",
        "degree": "Bachelor of Science",
        "field_of_study": "Computer Science",
        "graduation_year": "2012",
        "grade": "First Class"
      }
    ],
    "work_experience": [
      {
        "company": "Company Name",
        "position": "Software Developer",
        "duration": "2012-2020",
        "responsibilities": [
          "Developed web applications",
          "Managed database systems"
        ]
      }
    ],
    "skills_and_qualifications": [
      "Programming",
      "Project Management",
      "Database Design"
    ],
    "certifications": [
      "Certified Project Manager",
      "AWS Certified Developer"
    ],
    "achievements": [
      "Employee of the Year 2019",
      "Led successful project delivery"
    ]
  },
  "criteria_evaluation": [
    {
      "criterion_number": 1,
      "criterion_title": "The RS 3.2 must be signed",
      "evaluation_result": "MEETS",
      "evidence_found": [
        "Signature visible on page 2 of RS 3.2 form",
        "Date stamp present next to signature"
      ],
      "detailed_analysis": "The RS 3.2 form has been properly signed by the applicant. The signature is clear and matches the name on the application.",
      "score": "90",
      "recommendation": "Criterion fully satisfied"
    }
  ],
  "document_summary": {
    "files_analyzed": [
      "CV_John_Doe.pdf",
      "Academic_Certificates.pdf",
      "RS_3_2_Form.pdf"
    ],
    "key_information_found": [
      "Educational qualifications verified",
      "Work experience documented",
      "Required forms present"
    ],
    "document_types_identified": [
      "Curriculum Vitae",
      "Academic Certificate",
      "Application Form"
    ]
  },
  "overall_assessment": {
    "strengths": [
      "Strong educational background",
      "Relevant work experience",
      "Complete documentation"
    ],
    "weaknesses": [
      "Missing police clearance certificate",
      "Some documents not certified"
    ],
    "areas_for_clarification": [
      "Verify employment dates",
      "Confirm academic institution accreditation"
    ],
    "final_recommendation": "RECOMMENDED with minor clarifications needed"
  }
}
```

### Field Specifications

#### Analysis Summary
- **total_documents_analyzed** (integer): Number of document files processed
- **total_pages_analyzed** (integer): Total pages across all documents
- **analysis_date** (string): When analysis started
- **analysis_completion_time** (string): When analysis completed
- **processing_method** (string): Method used for analysis
- **overall_recommendation** (string): AI's final recommendation
- **confidence_score** (string): AI's confidence in the analysis

#### Applicant Profile
Structured extraction of applicant information from documents:
- **personal_information**: Basic personal details
- **education**: Array of educational qualifications
- **work_experience**: Array of employment history
- **skills_and_qualifications**: List of skills identified
- **certifications**: Professional certifications found
- **achievements**: Notable achievements mentioned

#### Document Summary
- **files_analyzed**: List of processed files
- **key_information_found**: Important information extracted
- **document_types_identified**: Types of documents recognized

#### Overall Assessment
- **strengths**: Positive aspects identified
- **weaknesses**: Areas of concern
- **areas_for_clarification**: Items needing verification
- **final_recommendation**: AI's overall recommendation

## Real-World Examples

### Example 1: Successful Pre-Screening
```json
{
  "analysis_date": "2025-08-11T10:30:00.000Z",
  "criteria_evaluations": [
    {
      "criterion_number": 1,
      "criterion_title": "The RS 3.2 must be signed",
      "evaluation_result": "MEETS",
      "score": 95,
      "evidence_found": ["Clear signature on RS 3.2 form"],
      "detailed_analysis": "Form properly signed and dated",
      "recommendation": "Criterion satisfied"
    },
    {
      "criterion_number": 2,
      "criterion_title": "Must have RS 3.2 Application form",
      "evaluation_result": "MEETS",
      "score": 100,
      "evidence_found": ["RS 3.2 form present in documents"],
      "detailed_analysis": "Complete RS 3.2 form provided",
      "recommendation": "Criterion satisfied"
    }
  ],
  "overall_summary": {
    "total_criteria": 2,
    "criteria_passed": 2,
    "criteria_failed": 0,
    "overall_recommendation": "RECOMMENDED",
    "confidence_score": "97%"
  }
}
```

### Example 2: Failed Pre-Screening
```json
{
  "analysis_date": "2025-08-11T11:00:00.000Z",
  "criteria_evaluations": [
    {
      "criterion_number": 1,
      "criterion_title": "Must be less than 65 Years of age",
      "evaluation_result": "DOES NOT MEET",
      "score": 0,
      "evidence_found": ["Date of birth: 1955-03-15"],
      "detailed_analysis": "Applicant is 70 years old, exceeds age limit",
      "recommendation": "Disqualified due to age requirement"
    }
  ],
  "overall_summary": {
    "total_criteria": 4,
    "criteria_passed": 3,
    "criteria_failed": 1,
    "overall_recommendation": "NOT RECOMMENDED",
    "confidence_score": "95%"
  }
}
```

## Empty State
When no evaluation has been performed:
```json
{
  "analysis_date": null,
  "criteria_evaluations": [],
  "overall_summary": {
    "total_criteria": 0,
    "criteria_passed": 0,
    "criteria_failed": 0,
    "overall_recommendation": "PENDING",
    "confidence_score": "0%"
  }
}
```

## Validation Rules

### JSON Structure Validation
- Must be valid JSON format
- Root object must contain required fields
- Arrays must contain objects with proper structure
- Dates must be in ISO 8601 format

### Field Validation
- **evaluation_result**: Must be one of the allowed values
- **score**: Must be integer between 0-100
- **criterion_number**: Must be positive integer
- **confidence_score**: Must be percentage string (e.g., "85%")

### CodeIgniter 4 Implementation
```php
// Validation in controller
if ($criteriaResults) {
    try {
        $decoded = json_decode($criteriaResults, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON format');
        }
        
        // Validate structure
        if (!isset($decoded['criteria_evaluations']) || !is_array($decoded['criteria_evaluations'])) {
            throw new Exception('Invalid criteria evaluations structure');
        }
        
        // Validate each evaluation
        foreach ($decoded['criteria_evaluations'] as $evaluation) {
            if (!isset($evaluation['criterion_number']) || !isset($evaluation['evaluation_result'])) {
                throw new Exception('Missing required evaluation fields');
            }
        }
    } catch (Exception $e) {
        // Handle validation error
        log_message('error', 'Pre-screening data validation failed: ' . $e->getMessage());
    }
}
```

## Usage in Application

### Storing Pre-Screening Results
```php
$criteriaResults = [
    'analysis_date' => date('c'),
    'criteria_evaluations' => $evaluations,
    'overall_summary' => $summary
];

$applicationModel->update($applicationId, [
    'pre_screened_criteria_results' => json_encode($criteriaResults),
    'pre_screened_ai_analysis' => json_encode($aiAnalysis),
    'pre_screened_status' => $status,
    'pre_screened_remarks' => $remarks,
    'pre_screened_at' => date('Y-m-d H:i:s'),
    'pre_screened_by' => $userId
]);
```

### Retrieving Pre-Screening Results
```php
$application = $applicationModel->find($applicationId);
$criteriaResults = [];
$aiAnalysis = [];

if (!empty($application['pre_screened_criteria_results'])) {
    $criteriaResults = json_decode($application['pre_screened_criteria_results'], true) ?? [];
}

if (!empty($application['pre_screened_ai_analysis'])) {
    $aiAnalysis = json_decode($application['pre_screened_ai_analysis'], true) ?? [];
}
```

## Related Files
- `app/Models/AppxApplicationDetailsModel.php`
- `app/Controllers/ApplicationPreScreeningController.php`
- `app/Views/application_pre_screening/prescreening_profile.php`
- `app/Views/application_pre_screening/application_pre_screening_detailed_view.php`

## Notes
- The structure supports both manual and AI-driven evaluations
- All timestamps should be in ISO 8601 format for consistency
- The structure is designed to be extensible for future enhancements
- Backward compatibility should be maintained when making changes

---
*Generated on: 2025-08-11*
*DERS Version: 1.0*
*Database: appx_application_details.pre_screened_criteria_results and pre_screened_ai_analysis fields*

<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreatePositionViewsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'position_id' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'comment'    => 'References positions.id',
            ],
            'viewer_ip' => [
                'type'       => 'VARCHAR',
                'constraint' => 45,
                'null'       => true,
                'comment'    => 'IP address of the viewer',
            ],
            'gps_coordinates' => [
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'null'       => true,
                'comment'    => 'GPS coordinates in format: latitude,longitude',
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Browser user agent string',
            ],
            'session_id' => [
                'type'       => 'VARCHAR',
                'constraint' => 128,
                'null'       => true,
                'comment'    => 'Session ID to prevent duplicate counting',
            ],
            'viewed_at' => [
                'type'    => 'DATETIME',
                'null'    => false,
                'comment' => 'When the position was viewed',
            ],
            'created_at' => [
                'type'    => 'DATETIME',
                'null'    => false,
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type'    => 'DATETIME',
                'null'    => false,
                'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_by' => [
                'type'       => 'INT',
                'constraint' => 11,
                'unsigned'   => true,
                'null'       => true,
            ],
        ]);

        // Set primary key
        $this->forge->addPrimaryKey('id');

        // Add indexes for better performance
        $this->forge->addKey('position_id');
        $this->forge->addKey('viewer_ip');
        $this->forge->addKey('session_id');
        $this->forge->addKey('viewed_at');
        $this->forge->addKey(['position_id', 'session_id']); // Composite index for duplicate prevention
        $this->forge->addKey('gps_coordinates'); // Index for GPS coordinates

        // Create the table
        $this->forge->createTable('position_views');

        // Add foreign key constraints (if supported by database engine)
        $this->db->query('ALTER TABLE position_views
            ADD CONSTRAINT fk_position_views_position_id
            FOREIGN KEY (position_id) REFERENCES positions(id)
            ON DELETE CASCADE ON UPDATE CASCADE');
    }

    public function down()
    {
        // Drop foreign key constraints first
        $this->db->query('ALTER TABLE position_views DROP FOREIGN KEY fk_position_views_position_id');

        // Drop the table
        $this->forge->dropTable('position_views');
    }
}

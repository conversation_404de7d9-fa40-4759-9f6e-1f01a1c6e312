<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class DakoiiUsersSeeder extends Seeder
{
    public function run(): void
    {
        $users = [
            [
                'name' => '<PERSON>',
                'username' => 'fkenny',
                'password' => 'dakoii', // This will be hashed by the model's beforeInsert callback
                'orgcode' => 'SYSTEM',
                'role' => 'dakoii',
                'is_active' => 1,
                'created_by' => null,
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Use the model to insert data so that validation and callbacks are triggered
        $dakoiiUsersModel = new \App\Models\DakoiiUsersModel();
        
        foreach ($users as $user) {
            // Check if user already exists
            $existingUser = $dakoiiUsersModel->where('username', $user['username'])->first();
            
            if (!$existingUser) {
                $dakoiiUsersModel->insert($user);
                echo "User '{$user['name']}' with username '{$user['username']}' has been seeded.\n";
            } else {
                echo "User with username '{$user['username']}' already exists. Skipping.\n";
            }
        }
    }
}

<?php

namespace App\Controllers;

class HomeMainController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    public function index()
    {
        try {
            // Load the PositionsModel
            $positionsModel = new \App\Models\PositionsModel();

            // Get positions from published exercises only
            $positions = $positionsModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.is_internal
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.status', 'active') // Only active positions
            ->where('exercises.status', 'published') // Only published exercises
            ->orderBy('RAND()') // Random order every time
            ->limit(30) // Limit to 30 positions for home page
            ->findAll();



            // Get statistics for the home page
            $exerciseModel = new \App\Models\ExerciseModel();
            $orgModel = new \App\Models\DakoiiOrgModel();

            // Count current openings (positions in published exercises)
            $currentOpenings = $positionsModel
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->where('positions.status', 'active')
                ->where('exercises.status', 'published')
                ->countAllResults();

            // Count total organizations
            $totalOrganizations = $orgModel->where('is_active', 1)->countAllResults();

            return view('home/home_home', [
                'title' => 'Home',
                'menu' => 'home',
                'navbar_fixed' => true,
                'latest_positions' => $positions,
                'current_openings' => $currentOpenings,
                'total_organizations' => $totalOrganizations
            ]);
        } catch (\Exception $e) {
            // Log the error and show empty positions
            log_message('error', 'Error loading home page positions: ' . $e->getMessage());

            return view('home/home_home', [
                'title' => 'Home',
                'menu' => 'home',
                'navbar_fixed' => true,
                'latest_positions' => [],
                'current_openings' => 0,
                'total_organizations' => 0,
                'error' => 'Unable to load current positions. Please try again later.'
            ]);
        }
    }

    public function about()
    {
        try {
            // Get statistics for the about page (same as home page)
            $positionsModel = new \App\Models\PositionsModel();
            $orgModel = new \App\Models\DakoiiOrgModel();

            // Count current openings (positions in published exercises)
            $currentOpenings = $positionsModel
                ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                ->where('positions.status', 'active')
                ->where('exercises.status', 'published')
                ->countAllResults();

            // Count total organizations
            $totalOrganizations = $orgModel->where('is_active', 1)->countAllResults();

            return view('home/home_about', [
                'title' => 'About',
                'menu' => 'about',
                'navbar_fixed' => true,
                'current_openings' => $currentOpenings,
                'total_organizations' => $totalOrganizations
            ]);
        } catch (\Exception $e) {
            // Log the error and show default values
            log_message('error', 'Error loading about page statistics: ' . $e->getMessage());

            return view('home/home_about', [
                'title' => 'About',
                'menu' => 'about',
                'navbar_fixed' => true,
                'current_openings' => 0,
                'total_organizations' => 0
            ]);
        }
    }

    public function howToApply()
    {
        return view('home/home_how_to_apply', [
            'title' => 'How to Apply',
            'menu' => 'how_to_apply',
            'navbar_fixed' => true
        ]);
    }

    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}

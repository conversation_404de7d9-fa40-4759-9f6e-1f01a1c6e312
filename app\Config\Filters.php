<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;
use CodeIgniter\Filters\CSRF;
use CodeIgniter\Filters\DebugToolbar;
use CodeIgniter\Filters\Honeypot;
use CodeIgniter\Filters\InvalidChars;
use CodeIgniter\Filters\SecureHeaders;

class Filters extends BaseConfig
{
    /**
     * Configures aliases for Filter classes to
     * make reading things nicer and simpler.
     */
    public array $aliases = [
        'csrf'          => CSRF::class,
        'toolbar'       => DebugToolbar::class,
        'honeypot'      => Honeypot::class,
        'invalidchars'  => InvalidChars::class,
        'secureheaders' => SecureHeaders::class,
        'auth'          => \App\Filters\Auth::class,
    ];

    /**
     * List of filter aliases that are always
     * applied before and after every request.
     */
    public array $globals = [
        'before' => [
            'csrf' => ['except' => [
                'api/*',
                'api/chatbot/*',
                'applicant/login',
                'applicant/check-email',
                'profile_applications_exercise/generate_profile/*',
                'applicant/jobs/final-submission/*'
            ]],
            'auth' => ['except' => [
                '/',
                'login',
                'logout',
                'about',
                'how-to-apply',
                'assets/*',
                'public/*',
                'jobs',
                'jobs/*',
                'dakoii',
                'dakoii/login',
                'applicant/*',
                'view_letter/*',
                'process_letter/*',
                'api/*',
                'api/chatbot/*'
            ]]
        ],
        'after' => [
            'toolbar',
        ],
    ];

    /**
     * List of filter aliases that works on a
     * particular HTTP method (GET, POST, etc.).
     */
    public array $methods = [];

    /**
     * List of filter aliases that should run on any
     * before or after URI patterns.
     */
    public array $filters = [];
}

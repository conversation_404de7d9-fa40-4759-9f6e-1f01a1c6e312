<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item active text-primary" aria-current="page"><i class="fas fa-plus me-1"></i>Create Organization</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h3 class="fw-bold text-light-text">Create New Organization</h3>
            <p class="text-secondary mb-0"><i class="fas fa-info-circle me-2"></i>Add a new organization to the system</p>
        </div>
        <a href="<?= base_url('dakoii/organization/list') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i> Back to List
        </a>
    </div>

    <!-- Create Form Card -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <h5 class="fw-bold mb-0 text-white">
                        <i class="fas fa-building me-2"></i>Organization Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <!-- Display validation errors -->
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?= form_open_multipart('dakoii/organization/create', ['class' => 'needs-validation', 'novalidate' => true]) ?>

                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-8">
                            <h6 class="text-primary fw-bold mb-3"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>

                            <div class="mb-4">
                                <label for="org_name" class="form-label fw-medium text-light-text">Organization Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control form-control-lg" id="org_name" name="org_name"
                                       value="<?= old('org_name') ?>" required placeholder="Enter organization name">
                                <div class="invalid-feedback">Please provide a valid organization name.</div>
                            </div>

                            <div class="mb-4">
                                <label for="description" class="form-label fw-medium text-light-text">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4"
                                          placeholder="Provide a brief description of the organization"><?= old('description') ?></textarea>
                            </div>

                            <h6 class="text-primary fw-bold mb-3 mt-4"><i class="fas fa-address-card me-2"></i>Contact Information</h6>

                            <div class="mb-4">
                                <label for="postal_address" class="form-label fw-medium text-light-text">Postal Address</label>
                                <textarea class="form-control" id="postal_address" name="postal_address" rows="3"
                                          placeholder="Enter postal address"><?= old('postal_address') ?></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="phone_numbers" class="form-label fw-medium text-light-text">Phone Numbers</label>
                                        <input type="text" class="form-control" id="phone_numbers" name="phone_numbers"
                                               value="<?= old('phone_numbers') ?>" placeholder="e.g., +************, +************">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-4">
                                        <label for="email_addresses" class="form-label fw-medium text-light-text">Email Address</label>
                                        <input type="email" class="form-control" id="email_addresses" name="email_addresses"
                                               value="<?= old('email_addresses') ?>" placeholder="e.g., <EMAIL>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-4">
                            <h6 class="text-primary fw-bold mb-3"><i class="fas fa-image me-2"></i>Organization Logo</h6>

                            <div class="text-center mb-4 p-3 bg-lighter-bg rounded">
                                <div id="logo-preview" class="mx-auto mb-3" style="width: 150px; height: 150px; border-radius: 50%;
                                                       background-color: var(--lighter-bg); border: 3px dashed #dee2e6;
                                                       display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-building text-primary" style="font-size: 3rem;"></i>
                                </div>
                                <input type="file" class="form-control" id="org_logo" name="org_logo" accept="image/*">
                                <div class="form-text text-secondary mt-2">
                                    <small><i class="fas fa-info-circle me-1"></i>Recommended size: 200x200 pixels</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Location Lock Settings (Optional) -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-lighter-bg border-0">
                                <div class="card-body p-3">
                                    <h6 class="text-primary fw-bold mb-3"><i class="fas fa-map-marker-alt me-2"></i>Location Lock Settings (Optional)</h6>
                                    <p class="text-secondary small mb-3">Restrict organization access to specific geographical locations</p>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="location_lock_country" class="form-label fw-medium text-light-text">Country</label>
                                                <select class="form-select" id="location_lock_country" name="location_lock_country">
                                                    <option value="">Select Country (Optional)</option>
                                                    <?php if (isset($countries) && !empty($countries)): ?>
                                                        <?php foreach ($countries as $country): ?>
                                                            <option value="<?= $country['id'] ?>" <?= old('location_lock_country') == $country['id'] ? 'selected' : '' ?>>
                                                                <?= esc($country['name']) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="location_lock_province" class="form-label fw-medium text-light-text">Province</label>
                                                <select class="form-select" id="location_lock_province" name="location_lock_province">
                                                    <option value="">Select Province (Optional)</option>
                                                    <?php if (isset($provinces) && !empty($provinces)): ?>
                                                        <?php foreach ($provinces as $province): ?>
                                                            <option value="<?= $province['id'] ?>" <?= old('location_lock_province') == $province['id'] ? 'selected' : '' ?>>
                                                                <?= esc($province['name']) ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_locationlocked" name="is_locationlocked" value="1"
                                               <?= old('is_locationlocked') ? 'checked' : '' ?>>
                                        <label class="form-check-label fw-medium text-light-text" for="is_locationlocked">
                                            Enable Location Lock
                                        </label>
                                        <div class="form-text text-secondary">
                                            <small><i class="fas fa-info-circle me-1"></i>When enabled, restricts organization access to selected location</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?= base_url('dakoii/organization/list') ?>" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg px-4">
                                    <i class="fas fa-save me-2"></i> Create Organization
                                </button>
                            </div>
                        </div>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Logo preview functionality
    const logoInput = document.getElementById('org_logo');
    const logoPreview = document.getElementById('logo-preview');

    if (logoInput && logoPreview) {
        logoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    logoPreview.innerHTML = `<img src="${e.target.result}" style="width: 150px; height: 150px; border-radius: 50%; object-fit: cover; border: 3px solid var(--primary);">`;
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    }

    // Form validation
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }

    // Auto-focus on organization name field
    const orgNameField = document.getElementById('org_name');
    if (orgNameField) {
        orgNameField.focus();
    }
});
</script>

<?= $this->endSection() ?>

<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="<?= base_url('applicant/application/' . $application['id']) ?>" class="btn btn-outline-primary mb-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Application
                    </a>
                    <h1 class="h3 mb-2">
                        Edit Application Files
                        <span class="badge bg-primary ms-2"><?= esc($application['application_number']) ?></span>
                    </h1>
                    <p class="text-muted">
                        <strong>Position:</strong> <?= esc($position['designation']) ?><br>
                        <strong>Organization:</strong> <?= esc($organization['org_name']) ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Upload New File Section -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>Upload New File
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open_multipart(base_url('applicant/application/' . $application['id'] . '/files/upload'), ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="mb-3">
                            <label for="file_title" class="form-label">File Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="file_title" name="file_title" 
                                   value="<?= old('file_title') ?>" required maxlength="255">
                            <div class="invalid-feedback">
                                Please provide a file title.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="file_description" class="form-label">File Description</label>
                            <textarea class="form-control" id="file_description" name="file_description" 
                                      rows="3" maxlength="500"><?= old('file_description') ?></textarea>
                            <div class="form-text">Optional description of the file content.</div>
                        </div>

                        <div class="mb-3">
                            <label for="file" class="form-label">Select File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="file" name="file" 
                                   accept=".pdf" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Only PDF files are allowed. Maximum file size: 25MB.
                            </div>
                            <div class="invalid-feedback">
                                Please select a valid PDF file.
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Upload File
                        </button>
                    <?= form_close() ?>
                </div>
            </div>
        </div>

        <!-- Current Files Section -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>Current Files (<?= count($files) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($files)): ?>
                        <div class="list-group">
                            <?php foreach ($files as $file): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                <?= esc($file['file_title']) ?>
                                            </h6>
                                            <?php if (!empty($file['file_description'])): ?>
                                                <p class="mb-1 text-muted small"><?= esc($file['file_description']) ?></p>
                                            <?php endif; ?>
                                            <small class="text-muted">
                                                Uploaded: <?= date('M d, Y', strtotime($file['created_at'])) ?>
                                            </small>
                                        </div>
                                        <div class="btn-group-vertical ms-3">
                                            <?php if (!empty($file['file_path'])): ?>
                                                <?php
                                                // Check if file exists using correct path
                                                $physicalPath = ROOTPATH . $file['file_path'];
                                                $fileExists = file_exists($physicalPath);
                                                ?>
                                                <?php if ($fileExists): ?>
                                                    <a href="<?= base_url($file['file_path']) ?>"
                                                       target="_blank"
                                                       class="btn btn-sm btn-outline-primary mb-1">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </a>
                                                <?php else: ?>
                                                    <span class="btn btn-sm btn-outline-secondary mb-1 disabled">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>File Missing
                                                    </span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="btn btn-sm btn-outline-secondary mb-1 disabled">
                                                    <i class="fas fa-question me-1"></i>No File Path
                                                </span>
                                            <?php endif; ?>
                                            
                                            <?= form_open(base_url('applicant/application/' . $application['id'] . '/files/delete/' . $file['id']), 
                                                ['class' => 'delete-form', 'style' => 'display: inline;']) ?>
                                                <button type="submit" class="btn btn-sm btn-outline-danger delete-btn">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </button>
                                            <?= form_close() ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No files uploaded yet.</p>
                            <p class="text-muted small">Use the upload form to add your first file.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>File Management Guidelines
                    </h6>
                    <ul class="mb-0 small">
                        <li>You can only edit files for applications with <strong>published</strong> exercise status.</li>
                        <li>Only PDF files are accepted with a maximum size of 25MB.</li>
                        <li>You can delete existing files and upload new ones, but cannot modify existing files.</li>
                        <li>Provide clear, descriptive titles for your files to help with identification.</li>
                        <li>All changes are immediately saved and cannot be undone.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item {
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group-vertical .btn {
    border-radius: 0.25rem;
}

.btn-group-vertical .btn + .btn {
    margin-top: 0.25rem;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });

    // Delete confirmation
    $('.delete-btn').on('click', function(e) {
        e.preventDefault();
        const form = $(this).closest('.delete-form');
        
        if (confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
            form.submit();
        }
    });

    // File input validation
    $('#file').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Check file size (25MB = 25 * 1024 * 1024 bytes)
            if (file.size > 25 * 1024 * 1024) {
                alert('File size must be less than 25MB.');
                this.value = '';
                return;
            }

            // Check file extension
            const fileName = file.name.toLowerCase();
            const fileExtension = fileName.split('.').pop();

            if (fileExtension !== 'pdf') {
                alert('Only PDF files are allowed. Selected file extension: ' + fileExtension);
                this.value = '';
                return;
            }

            // Check MIME type
            if (file.type !== 'application/pdf' && file.type !== '') {
                alert('Invalid file type. Only PDF files are allowed. Detected type: ' + file.type);
                this.value = '';
                return;
            }

            // Log file details for debugging
            console.log('File selected:', {
                name: file.name,
                size: file.size,
                type: file.type,
                extension: fileExtension
            });
        }
    });
});
</script>
<?= $this->endSection() ?>

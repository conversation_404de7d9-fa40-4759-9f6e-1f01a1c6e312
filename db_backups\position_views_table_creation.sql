-- Create position_views table for tracking position visitor statistics
-- This table will store information about who viewed which positions and from which province

CREATE TABLE `position_views` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `position_id` int(11) unsigned NOT NULL COMMENT 'References positions.id',
    `viewer_ip` varchar(45) DEFAULT NULL COMMENT 'IP address of the viewer',
    `gps_coordinates` varchar(50) DEFAULT NULL COMMENT 'GPS coordinates in format: latitude,longitude',
    `user_agent` text DEFAULT NULL COMMENT 'Browser user agent string',
    `session_id` varchar(128) DEFAULT NULL COMMENT 'Session ID to prevent duplicate counting',
    `viewed_at` datetime NOT NULL COMMENT 'When the position was viewed',
    `created_at` datetime NOT NULL DEFAULT current_timestamp(),
    `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `deleted_at` datetime DEFAULT NULL,
    `deleted_by` int(11) unsigned DEFAULT NULL,
    <PERSON><PERSON><PERSON><PERSON>Y (`id`),
    <PERSON><PERSON><PERSON> `idx_position_id` (`position_id`),
    <PERSON><PERSON><PERSON> `idx_viewer_ip` (`viewer_ip`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_viewed_at` (`viewed_at`),
    KEY `idx_position_session` (`position_id`, `session_id`),
    KEY `idx_gps_coordinates` (`gps_coordinates`),
    CONSTRAINT `fk_position_views_position_id` FOREIGN KEY (`position_id`) REFERENCES `positions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Tracks position views with geographical information';

-- Insert sample data for testing (optional)
-- INSERT INTO position_views (position_id, viewer_ip, gps_coordinates, session_id, viewed_at)
-- VALUES
-- (1, '*************', '-9.4438,147.1803', 'sample_session_1', NOW()),
-- (2, '*************', '-6.2088,106.8456', 'sample_session_2', NOW()),
-- (1, '*************', '40.7128,-74.0060', 'sample_session_3', NOW());

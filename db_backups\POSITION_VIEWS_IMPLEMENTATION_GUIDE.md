# Position Views Feature Implementation Guide

## Overview
This feature tracks how many times each position has been viewed and captures GPS coordinates of the viewers from around the world. The "Location" column in the positions report has been replaced with a "Views" column showing the view count for each position. GPS coordinates are stored in a simple "latitude,longitude" format for efficient storage and retrieval.

## Database Setup

### 1. Create the position_views table
Run the SQL script located at: `db_backups/position_views_table_creation.sql`

Or use the CodeIgniter migration:
```bash
php spark migrate
```

### 2. Verify Table Creation
The `position_views` table should have the following structure:
- `id` - Primary key
- `position_id` - Foreign key to positions table
- `viewer_ip` - IP address of viewer
- `gps_coordinates` - GPS coordinates in format "latitude,longitude" (varchar 50)
- `user_agent` - Browser user agent
- `session_id` - Session ID for duplicate prevention
- `viewed_at` - Timestamp of view
- `created_at`, `updated_at`, `deleted_at` - Standard timestamps

## Features Implemented

### 1. View Tracking
- Automatically tracks when positions are viewed
- Prevents duplicate counting from same session within 60 minutes
- Captures geographical location (province) when possible
- Tracks views from:
  - Public job listings (`/jobs/view/{id}`)
  - Applicant position details (`/applicant/jobs/position/{id}`)
  - Admin position views (`/positions/show/{id}`)

### 2. GPS Coordinates Tracking
- Captures precise GPS coordinates (latitude/longitude) from IP address
- Uses multiple free geolocation APIs (ipapi.co, ip-api.com, ipinfo.io)
- Stores coordinates in simple "latitude,longitude" format for efficiency
- Global reach - works for visitors from anywhere in the world
- Lightweight storage - single field instead of multiple location columns

### 3. Reports Integration
- Positions report now shows "Views" instead of "Location"
- Total views summary in dashboard statistics
- View counts displayed with eye icon and badge styling

## Testing the Implementation

### 1. Database Tests
Visit: `http://localhost/ders/test/position-views/database`
- Checks if table exists
- Verifies table structure
- Tests basic insert operations

### 2. Functionality Tests
Visit: `http://localhost/ders/test/position-views`
- Tests view tracking
- Tests duplicate prevention
- Tests view count retrieval
- Tests geographical statistics

### 3. Scenario Tests
Visit: `http://localhost/ders/test/position-views/scenarios`
- Tests normal view tracking
- Tests forced tracking (bypass duplicates)
- Tests tracking with province data
- Tests invalid position handling

### 4. Manual Testing
1. Visit a position: `http://localhost/ders/jobs/view/1`
2. Check the positions report: `http://localhost/ders/reports/positions-report/3`
3. Verify view count increased
4. Test province selection: `http://localhost/ders/location/select-province`

## Usage Instructions

### For End Users
1. **Viewing Positions**: Simply visit any position page - views are tracked automatically
2. **Setting Location**: Visit `/location/select-province` to manually set your province for better tracking

### For Administrators
1. **View Statistics**: Check the positions report to see view counts
2. **Geographical Analysis**: Access comprehensive analytics at `/analytics/positions`
3. **Position Analytics**: View detailed analytics for specific positions
4. **Exercise Analytics**: View aggregated analytics for entire exercises
5. **Interactive Maps**: View visitor locations on interactive maps with coordinates
6. **Clear Test Data**: Use `/test/position-views/clear` to remove test records

## API Endpoints

### Public Routes
- `GET /location/select-province` - Province selection form
- `POST /location/set-province` - Set user's province
- `GET /location/current-province` - Get current province (AJAX)
- `POST /location/clear-province` - Clear province cache

### Analytics Routes
- `GET /analytics/positions` - Main analytics dashboard
- `GET /analytics/position/{id}` - Position-specific analytics
- `GET /analytics/exercise/{id}` - Exercise-wide analytics
- `GET /analytics/coordinates/{id?}` - Get coordinates for mapping (AJAX)
- `GET /analytics/countries/{id?}` - Get country statistics (AJAX)
- `GET /analytics/regions/{id?}` - Get region statistics (AJAX)

### Test Routes (Development Only)
- `GET /test/position-views` - Basic functionality tests
- `GET /test/position-views/database` - Database connectivity tests
- `GET /test/position-views/scenarios` - Scenario-based tests
- `GET /test/position-views/clear` - Clear test data

## Configuration

### Geolocation Services
The system uses free geolocation APIs in this order:
1. ipapi.co (1000 requests/day)
2. ip-api.com (1000 requests/hour)
3. ipinfo.io (50,000 requests/month)

### Duplicate Prevention
- Views from the same session are only counted once per hour
- This prevents inflated counts from page refreshes or multiple visits

### Performance Considerations
- View tracking is asynchronous and won't slow down page loads
- Database queries are optimized with proper indexes
- Failed tracking attempts are logged but don't break functionality

## Troubleshooting

### Common Issues
1. **Views not being tracked**: Check if position_views table exists and has proper permissions
2. **Geographical data missing**: This is normal - not all IPs can be geolocated
3. **Duplicate views**: Check session handling and duplicate prevention logic

### Error Logs
Check CodeIgniter logs for any tracking errors:
- Location: `writable/logs/`
- Look for entries containing "ViewTrackingService" or "GeolocationHelper"

### Database Issues
If you encounter foreign key constraint errors:
1. Ensure positions table has the referenced position IDs
2. Ensure geo_provinces table has valid province data
3. Check that the foreign key constraints are properly created

## Geographical Analytics Dashboard
The comprehensive analytics system provides:
- **Global View Statistics**: Country, region, and city-level breakdowns
- **Interactive Maps**: Precise visitor locations with coordinates
- **Time-based Trends**: Daily view statistics and historical data
- **Position Analytics**: Detailed insights for individual positions
- **Exercise Analytics**: Aggregated statistics for entire recruitment exercises
- **Real-time Data**: Live updates as new views are recorded

## Key Features
- **Precise Coordinates**: Latitude/longitude tracking for exact locations
- **Global Reach**: Supports visitors from anywhere in the world
- **Multiple Data Sources**: Uses 3 different geolocation APIs for reliability
- **Performance Optimized**: Efficient database queries with proper indexing
- **Privacy Focused**: Only stores necessary geographical data
- **Backward Compatible**: Maintains existing province-based functionality

This implementation provides a comprehensive foundation for global position visitor analytics while maintaining system performance and user privacy.

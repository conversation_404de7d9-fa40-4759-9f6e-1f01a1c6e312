<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddPreScreenedAiAnalysisColumn extends Migration
{
    public function up()
    {
        // Add the pre_screened_ai_analysis column to appx_application_details table
        $this->forge->addColumn('appx_application_details', [
            'pre_screened_ai_analysis' => [
                'type' => 'LONGTEXT',
                'null' => true,
                'comment' => 'AI analysis data in JSON format for pre-screening results',
                'after' => 'pre_screened_criteria_results'
            ]
        ]);
    }

    public function down()
    {
        // Remove the pre_screened_ai_analysis column
        $this->forge->dropColumn('appx_application_details', 'pre_screened_ai_analysis');
    }
}

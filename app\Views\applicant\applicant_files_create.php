<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">Upload New File</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/dashboard') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/profile#files') ?>">Profile</a></li>
                            <li class="breadcrumb-item active">Upload File</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                </a>
            </div>

            <!-- Upload Form Card -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload me-2"></i>Upload Document
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Display validation errors -->
                            <?php if (session()->getFlashdata('errors')): ?>
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                            <li><?= esc($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Display success message -->
                            <?php if (session()->getFlashdata('success')): ?>
                                <div class="alert alert-success">
                                    <?= session()->getFlashdata('success') ?>
                                </div>
                            <?php endif; ?>

                            <!-- Display error message -->
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger">
                                    <?= session()->getFlashdata('error') ?>
                                </div>
                            <?php endif; ?>

                            <form action="<?= base_url('applicant/profile/files/store') ?>" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <?= csrf_field() ?>
                                
                                <!-- Upload Form -->
                                <div class="row g-3">

                                    <!-- File Title and Description (Hidden initially) -->
                                    <div class="col-12" id="titleDescriptionSection" style="display: none;">
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-edit me-2"></i>File Information
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-3">
                                                    <div class="col-12">
                                                        <label class="form-label">File Title <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" name="file_title" value="<?= old('file_title') ?>" required>
                                                        <div class="invalid-feedback">
                                                            Please provide a file title.
                                                        </div>
                                                        <div class="form-text">
                                                            AI-generated title based on document content (you can edit this)
                                                        </div>
                                                    </div>

                                                    <div class="col-12">
                                                        <label class="form-label">File Description</label>
                                                        <textarea class="form-control" name="file_description" rows="3" placeholder="AI-generated description..."><?= old('file_description') ?></textarea>
                                                        <div class="form-text">
                                                            AI-generated description based on document content (you can edit this)
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label">Select PDF File <span class="text-danger">*</span></label>
                                        <div class="file-input-wrapper">
                                            <input type="file" class="form-control" id="pdfFileInput" accept=".pdf" required>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>Only PDF files are supported. Maximum size: 25MB
                                            </div>
                                        </div>

                                        <!-- Process and Upload Button (Hidden initially) -->
                                        <div class="mt-3" id="processUploadSection" style="display: none;">
                                            <button type="button" class="btn btn-success btn-lg" id="processUploadBtn">
                                                <i class="fas fa-cogs me-2"></i>Process and Upload
                                            </button>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>This will automatically split, convert, analyze with AI, and upload your document
                                            </div>
                                        </div>

                                        <!-- File Information and Processing Status (Hidden initially) -->
                                        <div class="mt-3" id="fileInfo" style="display: none;">
                                            <div class="card border-info">
                                                <div class="card-header bg-info text-white">
                                                    <h6 class="mb-0">
                                                        <i class="fas fa-file-pdf me-2"></i>File Information & Processing Status
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row g-3 mb-3">
                                                        <div class="col-md-6">
                                                            <div class="info-item">
                                                                <label class="info-label">File Name:</label>
                                                                <span class="info-value" id="fileName"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="info-item">
                                                                <label class="info-label">File Size:</label>
                                                                <span class="info-value" id="fileSize"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="info-item">
                                                                <label class="info-label">Total Pages:</label>
                                                                <span class="info-value" id="totalPages"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="info-item">
                                                                <label class="info-label">Split Sets:</label>
                                                                <span class="info-value" id="splitSets">-</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Processing Status -->
                                                    <div id="processingStatus">
                                                        <hr>
                                                        <h6 class="text-muted mb-3">Processing Status</h6>
                                                        <div class="row g-2">
                                                            <div class="col-md-6">
                                                                <div class="status-item">
                                                                    <i class="fas fa-cut me-2 text-muted" id="splitIcon"></i>
                                                                    <span class="status-text" id="splitStatus">PDF Splitting: Pending</span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="status-item">
                                                                    <i class="fas fa-cogs me-2 text-muted" id="imageIcon"></i>
                                                                    <span class="status-text" id="imageStatus">AI Readable Format: Pending</span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="status-item">
                                                                    <i class="fas fa-robot me-2 text-muted" id="aiIcon"></i>
                                                                    <span class="status-text" id="aiStatus">AI Document Analysis: Pending</span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="status-item">
                                                                    <i class="fas fa-upload me-2 text-muted" id="uploadIcon"></i>
                                                                    <span class="status-text" id="uploadStatus">File Upload: Pending</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Progress Section (Hidden initially) -->
                                    <div class="col-12" id="progressSection" style="display: none;">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-cogs me-2"></i>Processing Progress
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="status-text mb-2" id="statusText">Processing...</div>
                                                <div class="progress mb-2" style="height: 20px;">
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                                                         id="progressFill"
                                                         role="progressbar"
                                                         style="width: 0%"
                                                         aria-valuenow="0"
                                                         aria-valuemin="0"
                                                         aria-valuemax="100">
                                                        <span id="progressText">0%</span>
                                                    </div>
                                                </div>
                                                <div class="timing-info" id="timingInfo">
                                                    <small class="text-muted">
                                                        <strong>Elapsed:</strong> <span id="elapsedTime">0s</span> |
                                                        <strong>Status:</strong> <span id="currentStatus">Starting...</span>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Processed Text Display (Hidden initially) -->
                                <div class="col-12" id="extractedTextSection" style="display: none;">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-file-alt me-2"></i>AI Analysis Results
                                                </h6>
                                                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="collapse" data-bs-target="#extractedTextCollapse">
                                                    <i class="fas fa-eye me-1"></i>View Results
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="collapse" id="extractedTextCollapse">
                                                <div class="bg-light p-3 rounded">
                                                    <pre id="extractedTextContent" style="max-height: 400px; overflow-y: auto; white-space: pre-wrap; font-size: 12px;"></pre>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-center mt-4">
                                    <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Profile
                                    </a>
                                </div>

                                <!-- Hidden fields for extracted data -->
                                <input type="hidden" name="extracted_text" id="hiddenExtractedText">
                                <input type="hidden" name="file_data" id="hiddenFileData">

                                <!-- Hidden file input for actual file upload -->
                                <input type="file" name="file" id="hiddenFileInput" style="display: none;" accept=".pdf">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.custom-file-input-wrapper {
    margin-bottom: 1rem;
}

.custom-file-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.custom-file-input:hover {
    border-color: #0d6efd;
    background-color: #f0f8ff;
}

.upload-steps {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Progress bar enhancements */
.progress {
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.progress-bar {
    transition: width 0.6s ease;
    font-weight: 600;
    font-size: 12px;
    line-height: 20px;
    text-align: center;
    color: white;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* Status text styling */
#statusText {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

#currentStatus {
    color: #6c757d;
    font-style: italic;
}

.step-item {
    padding: 8px 0;
    transition: all 0.2s ease;
}

.step-text {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.step-item.active .step-text {
    color: #dc3545;
}

.step-item.completed .step-text {
    color: #198754;
}

.step-icon {
    margin: 0 8px;
    font-size: 12px;
}

.step-item.active .step-icon {
    color: #dc3545 !important;
    animation: pulse 1.5s infinite;
}

.step-item.completed .step-icon {
    color: #198754 !important;
}

.step-item.completed .step-icon:before {
    content: "\f00c"; /* FontAwesome check mark */
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.progress-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
}

.timing-info {
    font-size: 14px;
}

.extracted-text-section {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* File Information Styles */
.info-item {
    margin-bottom: 8px;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.info-value {
    color: #212529;
    font-size: 14px;
    margin-left: 8px;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 6px 0;
    font-size: 13px;
}

.status-text {
    color: #6c757d;
}

.status-item.processing .status-text {
    color: #dc3545;
    font-weight: 500;
}

.status-item.completed .status-text {
    color: #198754;
    font-weight: 500;
}

.status-item.processing i {
    color: #dc3545 !important;
    animation: pulse 1.5s infinite;
}

.status-item.completed i {
    color: #198754 !important;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script src="https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js"></script>
<script>
// Gemini AI Configuration
const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
const GEMINI_MODEL = 'gemini-2.5-flash';
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

let selectedFile = null;
let pdfDocument = null;
let totalPages = 0;
let extractedText = '';
let processingStartTime = null;
let timingInterval = null;
let convertedPageSets = []; // Store pre-converted page images organized by sets
let allPagesConverted = false;

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

$(document).ready(function() {
    // File input handling - single event listener
    document.getElementById('pdfFileInput').addEventListener('change', handleFileSelect);

    // Process and Upload button handling
    document.getElementById('processUploadBtn').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        startAutomaticProcessing();
    });

    // Form submission is now handled automatically in the startAutomaticProcessing function

    // Real-time validation for file title
    const fileTitleInput = document.querySelector('input[name="file_title"]');
    if (fileTitleInput) {
        fileTitleInput.addEventListener('input', function() {
            const value = this.value.trim();
            if (value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });

        fileTitleInput.addEventListener('blur', function() {
            const value = this.value.trim();
            if (!value) {
                this.classList.add('is-invalid');
            }
        });
    }

    // Real-time validation for file description (optional but good UX)
    const fileDescInput = document.querySelector('textarea[name="file_description"]');
    if (fileDescInput) {
        fileDescInput.addEventListener('input', function() {
            const value = this.value.trim();
            if (value) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid', 'is-invalid');
            }
        });
    }
});

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
        // Check file size (25MB = 25 * 1024 * 1024 bytes)
        const maxSize = 25 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('File size must be less than 25MB');
            e.target.value = ''; // Clear the input
            return;
        }

        selectedFile = file;

        // Show file information
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);

        // Show Process and Upload button
        document.getElementById('processUploadSection').style.display = 'block';

        // Clear any previous processing data
        clearPreviousFileData();

    } else {
        alert('Please select a valid PDF file.');
        e.target.value = ''; // Clear the input
    }
}

// New function to start the automatic processing sequence
async function startAutomaticProcessing() {
    if (!selectedFile) {
        alert('Please select a file first.');
        return;
    }

    // Disable the process button and show processing
    const processBtn = document.getElementById('processUploadBtn');
    processBtn.disabled = true;
    processBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

    try {
        // Load PDF document
        const arrayBuffer = await selectedFile.arrayBuffer();
        pdfDocument = await pdfjsLib.getDocument(arrayBuffer).promise;
        totalPages = pdfDocument.numPages;

        // Calculate split sets
        const splitSets = totalPages > 20 ? Math.ceil(totalPages / 10) : Math.ceil(totalPages / 5);

        // Update file information display
        document.getElementById('totalPages').textContent = totalPages;
        document.getElementById('splitSets').textContent = splitSets;

        // Show file info and processing status
        document.getElementById('fileInfo').style.display = 'block';
        document.getElementById('progressSection').style.display = 'block';

        // Reset all status indicators
        resetProcessingStatus();

        // Initialize progress bar
        const progressBar = document.getElementById('progressFill');
        progressBar.style.width = '0%';
        progressBar.setAttribute('aria-valuenow', 0);
        document.getElementById('progressText').textContent = '0%';
        document.getElementById('statusText').textContent = 'Starting processing...';
        document.getElementById('currentStatus').textContent = 'Initializing...';

        showSuccess(`PDF loaded successfully! ${totalPages} pages found, will be split into ${splitSets} sets. Starting automatic processing...`);

        // Step 1: Split PDF and convert to images
        updateProcessingStatus('split', 'processing', 'PDF Splitting: Starting...');
        await convertAllPagesToImages();

        // Step 2: Process with AI (automatically after conversion)
        updateProcessingStatus('ai', 'processing', 'AI Document Analysis: Starting...');
        await processFileWithAI();

        // Step 3: Upload file (automatically after AI processing)
        updateProcessingStatus('upload', 'processing', 'File Upload: Starting...');
        await uploadFileAutomatically();

        // Mark all steps as completed
        updateProcessingStatus('upload', 'completed', 'File Upload: Completed successfully');

        showSuccess('File processed and uploaded successfully!');

        // Redirect to profile page after successful upload
        setTimeout(() => {
            window.location.href = '<?= base_url('applicant/profile#files') ?>';
        }, 2000);

    } catch (error) {
        showError('Error during processing: ' + error.message);

        // Re-enable the button
        processBtn.disabled = false;
        processBtn.innerHTML = '<i class="fas fa-cogs me-2"></i>Process and Upload';
    }
}

async function convertAllPagesToImages() {
    try {
        // Update status to show conversion starting
        updateProcessingStatus('split', 'processing', 'PDF Splitting: Starting...');
        updateProcessingStatus('image', 'processing', 'AI Readable Format: Starting...');

        const totalSets = Math.ceil(totalPages / 5);
        convertedPageSets = [];

        for (let setIndex = 0; setIndex < totalSets; setIndex++) {
            const startPage = setIndex * 5 + 1;
            const endPage = Math.min((setIndex + 1) * 5, totalPages);

            // Update progress bar for conversion
            const conversionProgress = Math.round((setIndex / totalSets) * 100);
            const progressBar = document.getElementById('progressFill');
            progressBar.style.width = conversionProgress + '%';
            progressBar.setAttribute('aria-valuenow', conversionProgress);
            document.getElementById('progressText').textContent = conversionProgress + '%';
            document.getElementById('statusText').textContent =
                `Converting set ${setIndex + 1} of ${totalSets} (Pages ${startPage}-${endPage})`;
            document.getElementById('currentStatus').textContent =
                `Converting pages ${startPage}-${endPage} to images...`;

            // Update status for current set
            updateProcessingStatus('split', 'processing', `PDF Splitting: Set ${setIndex + 1}/${totalSets} (Pages ${startPage}-${endPage})`);
            updateProcessingStatus('image', 'processing', `AI Readable Format: Set ${setIndex + 1}/${totalSets} (Pages ${startPage}-${endPage})`);

            // Convert pages in this set to images
            const setImages = [];
            for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
                try {
                    const page = await pdfDocument.getPage(pageNum);
                    const scale = 2.0; // High resolution for better text recognition
                    const viewport = page.getViewport({ scale });

                    // Create canvas for image conversion
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // Render PDF page to canvas
                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert to base64 PNG image
                    const imageData = canvas.toDataURL('image/png').split(',')[1];
                    setImages.push({
                        pageNumber: pageNum,
                        data: imageData
                    });

                } catch (error) {
                    console.error(`Error converting page ${pageNum}:`, error);
                    // Add placeholder for failed page
                    setImages.push({
                        pageNumber: pageNum,
                        data: null,
                        error: error.message
                    });
                }
            }

            // Store the converted set
            convertedPageSets.push({
                setNumber: setIndex + 1,
                startPage: startPage,
                endPage: endPage,
                images: setImages
            });

            // Small delay to prevent UI blocking
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Complete the conversion progress bar
        const progressBar = document.getElementById('progressFill');
        progressBar.style.width = '100%';
        progressBar.setAttribute('aria-valuenow', 100);
        document.getElementById('progressText').textContent = '100%';
        document.getElementById('statusText').textContent = 'Conversion completed';
        document.getElementById('currentStatus').textContent = 'All pages converted successfully';

        // Mark splitting and conversion as completed
        updateProcessingStatus('split', 'completed', 'PDF Splitting: All sets completed');
        updateProcessingStatus('image', 'completed', 'AI Readable Format: All pages converted successfully');

        // Set flag
        allPagesConverted = true;

        showSuccess(`All ${totalPages} pages converted to AI readable format successfully! Ready for AI document analysis.`);

    } catch (error) {
        showError('Error converting to AI readable format: ' + error.message);
        updateProcessingStatus('split', 'error', 'PDF Splitting: Failed');
        updateProcessingStatus('image', 'error', 'AI Readable Format: Failed');
    }
}

// This function is no longer needed as we use automatic processing

// New function for automatic AI processing
async function processFileWithAI() {
    if (!selectedFile || !pdfDocument || !allPagesConverted) {
        throw new Error('Please wait for page conversion to complete first.');
    }

    // Initialize timing
    processingStartTime = Date.now();
    extractedText = '';

    // Start timing updates
    startTimingUpdates();

    // Reset progress bar for AI processing phase
    const progressBar = document.getElementById('progressFill');
    progressBar.style.width = '0%';
    progressBar.setAttribute('aria-valuenow', 0);
    document.getElementById('progressText').textContent = '0%';
    document.getElementById('statusText').textContent = 'Starting AI analysis...';
    document.getElementById('currentStatus').textContent = 'Preparing for AI processing...';

    try {
        const totalSets = convertedPageSets.length;

        for (let setIndex = 0; setIndex < totalSets; setIndex++) {
            const pageSet = convertedPageSets[setIndex];
            const { setNumber, startPage, endPage, images } = pageSet;

            // Update progress bar
            const overallProgress = Math.round((setIndex / totalSets) * 100);
            const progressBar = document.getElementById('progressFill');
            progressBar.style.width = overallProgress + '%';
            progressBar.setAttribute('aria-valuenow', overallProgress);
            document.getElementById('progressText').textContent = overallProgress + '%';
            document.getElementById('statusText').textContent =
                `Processing set ${setNumber} of ${totalSets} (Pages ${startPage}-${endPage})`;
            document.getElementById('currentStatus').textContent =
                `AI analyzing pages ${startPage}-${endPage}...`;

            // Update AI processing status
            updateProcessingStatus('ai', 'processing', `AI Document Analysis: Set ${setNumber}/${totalSets} (Pages ${startPage}-${endPage})`);

            // Send pre-converted images to Gemini AI
            const setText = await extractTextWithGemini(images, startPage, endPage);

            // Append to full text
            if (setText) {
                extractedText += `\n\n## Pages ${startPage}-${endPage}\n\n${setText}`;
            }

            // Small delay to prevent rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Complete the progress bar
        const progressBar = document.getElementById('progressFill');
        progressBar.style.width = '100%';
        progressBar.setAttribute('aria-valuenow', 100);
        document.getElementById('progressText').textContent = '100%';
        document.getElementById('statusText').textContent = 'AI analysis completed';
        document.getElementById('currentStatus').textContent = 'Finalizing results...';

        // Mark AI processing as completed
        updateProcessingStatus('ai', 'completed', 'AI Document Analysis: All sets completed');

        // Stop timing updates
        stopTimingUpdates();

        // Generate title and description using AI
        await generateTitleAndDescription();

        // Show title and description section
        document.getElementById('titleDescriptionSection').style.display = 'block';

        // Display the extracted text
        displayExtractedText();

        const totalTime = Date.now() - processingStartTime;
        showSuccess(`AI analysis completed successfully! Total time: ${formatTime(totalTime)}.`);

    } catch (error) {
        // Stop timing updates on error
        stopTimingUpdates();
        updateProcessingStatus('ai', 'error', 'AI Document Analysis: Failed');
        throw error;
    }
}

// Function to display extracted text in a formatted way
function displayExtractedText() {
    try {
        const jsonData = JSON.parse(extractedText.replace(/^.*?## Pages \d+-\d+\s*/, ''));
        let formattedDisplay = '';

        if (jsonData.pages && Array.isArray(jsonData.pages)) {
            jsonData.pages.forEach(page => {
                formattedDisplay += `<div class="page-section mb-4 p-3 border rounded">`;
                formattedDisplay += `<h6 class="text-primary mb-3">Page ${page.page_number}</h6>`;

                if (page.content_type) {
                    formattedDisplay += `<div class="mb-2"><strong>Content Type:</strong> ${page.content_type}</div>`;
                }
                if (page.content_date) {
                    formattedDisplay += `<div class="mb-2"><strong>Content Date:</strong> ${page.content_date}</div>`;
                }
                if (page.content_elements && Array.isArray(page.content_elements)) {
                    formattedDisplay += `<div class="mb-2"><strong>Content Elements:</strong> ${page.content_elements.join(', ')}</div>`;
                }
                if (page.content_brief) {
                    formattedDisplay += `<div class="mb-2"><strong>Content Brief:</strong> ${page.content_brief}</div>`;
                }
                if (page.content_additional_elements) {
                    formattedDisplay += `<div class="mb-2"><strong>Additional Elements:</strong> ${page.content_additional_elements}</div>`;
                }

                formattedDisplay += `</div>`;
            });
        } else {
            // Fallback for non-JSON format
            formattedDisplay = `<div class="p-3 bg-light rounded" style="white-space: pre-wrap;">${extractedText}</div>`;
        }

        document.getElementById('extractedTextContent').innerHTML = formattedDisplay;
        document.getElementById('extractedTextSection').style.display = 'block';

    } catch (error) {
        // If JSON parsing fails, display as plain text
        console.warn('Failed to parse JSON, displaying as plain text:', error);
        document.getElementById('extractedTextContent').innerHTML = `<div class="p-3 bg-light rounded" style="white-space: pre-wrap;">${extractedText}</div>`;
        document.getElementById('extractedTextSection').style.display = 'block';
    }
}

// New function for automatic file upload
async function uploadFileAutomatically() {
    try {
        // Reset progress bar for upload phase
        const progressBar = document.getElementById('progressFill');
        progressBar.style.width = '0%';
        progressBar.setAttribute('aria-valuenow', 0);
        document.getElementById('progressText').textContent = '0%';
        document.getElementById('statusText').textContent = 'Preparing file upload...';
        document.getElementById('currentStatus').textContent = 'Preparing form data...';

        // Prepare form data
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('file_title', document.querySelector('input[name="file_title"]').value || 'AI Generated Title');
        formData.append('file_description', document.querySelector('textarea[name="file_description"]').value || 'AI Generated Description');
        formData.append('extracted_text', extractedText);

        // Add CSRF token
        const csrfToken = document.querySelector('input[name="csrf_test_name"]').value;
        formData.append('csrf_test_name', csrfToken);

        // Update progress
        progressBar.style.width = '25%';
        progressBar.setAttribute('aria-valuenow', 25);
        document.getElementById('progressText').textContent = '25%';
        document.getElementById('statusText').textContent = 'Uploading file...';
        document.getElementById('currentStatus').textContent = 'Sending file to server...';

        // Upload file
        const response = await fetch('<?= base_url('applicant/profile/files/store') ?>', {
            method: 'POST',
            body: formData
        });

        // Update progress
        progressBar.style.width = '75%';
        progressBar.setAttribute('aria-valuenow', 75);
        document.getElementById('progressText').textContent = '75%';
        document.getElementById('currentStatus').textContent = 'Processing server response...';

        if (!response.ok) {
            throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.text();

        // Check if upload was successful (you might need to adjust this based on your response format)
        if (result.includes('success') || response.status === 200) {
            // Complete the progress bar
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', 100);
            document.getElementById('progressText').textContent = '100%';
            document.getElementById('statusText').textContent = 'Upload completed';
            document.getElementById('currentStatus').textContent = 'File uploaded successfully';

            updateProcessingStatus('upload', 'completed', 'File Upload: Completed successfully');
            return true;
        } else {
            throw new Error('Upload failed - server response indicates error');
        }

    } catch (error) {
        updateProcessingStatus('upload', 'error', 'File Upload: Failed');
        throw error;
    }
}

// Split PDF into chunks using PDF-lib
async function splitPdfIntoChunks(file, pagesPerChunk) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
        const totalPages = pdfDoc.getPageCount();
        const chunks = [];

        for (let i = 0; i < totalPages; i += pagesPerChunk) {
            const startPage = i + 1;
            const endPage = Math.min(i + pagesPerChunk, totalPages);

            // Create new PDF document for this chunk
            const chunkDoc = await PDFLib.PDFDocument.create();

            // Copy pages to chunk document
            const pageIndices = [];
            for (let j = i; j < Math.min(i + pagesPerChunk, totalPages); j++) {
                pageIndices.push(j);
            }

            const copiedPages = await chunkDoc.copyPages(pdfDoc, pageIndices);
            copiedPages.forEach((page) => chunkDoc.addPage(page));

            // Serialize the chunk PDF
            const pdfBytes = await chunkDoc.save();

            chunks.push({
                startPage: startPage,
                endPage: endPage,
                pdfBytes: pdfBytes
            });
        }

        return chunks;
    } catch (error) {
        console.error('Error splitting PDF:', error);
        // Fallback to original method if PDF-lib fails
        return await splitPdfFallback(file, pagesPerChunk);
    }
}

// Fallback method using PDF.js only
async function splitPdfFallback(file, pagesPerChunk) {
    const arrayBuffer = await file.arrayBuffer();
    const pdfDoc = await pdfjsLib.getDocument(arrayBuffer).promise;
    const totalPages = pdfDoc.numPages;
    const chunks = [];

    for (let i = 0; i < totalPages; i += pagesPerChunk) {
        const startPage = i + 1;
        const endPage = Math.min(i + pagesPerChunk, totalPages);

        chunks.push({
            startPage: startPage,
            endPage: endPage,
            pdfBytes: arrayBuffer // Use original PDF for fallback
        });
    }

    return chunks;
}

// Convert PDF chunk to images using PDF.js
async function convertPdfChunkToImages(pdfBytes, startPage, endPage) {
    const images = [];

    try {
        // Load the PDF chunk with PDF.js
        const pdfDoc = await pdfjsLib.getDocument({ data: pdfBytes }).promise;

        // For fallback case, we need to extract specific pages
        const isFullPdf = pdfDoc.numPages > (endPage - startPage + 1);

        if (isFullPdf) {
            // Extract specific pages from full PDF
            for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
                try {
                    const page = await pdfDoc.getPage(pageNum);
                    const scale = 2.0; // Higher scale for better text recognition
                    const viewport = page.getViewport({ scale });

                    // Create canvas
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // Render page to canvas
                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert to base64
                    const imageData = canvas.toDataURL('image/png').split(',')[1];
                    images.push({
                        pageNumber: pageNum,
                        data: imageData
                    });

                } catch (error) {
                    console.error(`Error converting page ${pageNum} to image:`, error);
                }
            }
        } else {
            // Process chunk PDF (all pages)
            const numPages = pdfDoc.numPages;
            for (let pageNum = 1; pageNum <= numPages; pageNum++) {
                try {
                    const page = await pdfDoc.getPage(pageNum);
                    const scale = 2.0; // Higher scale for better text recognition
                    const viewport = page.getViewport({ scale });

                    // Create canvas
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // Render page to canvas
                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert to base64
                    const imageData = canvas.toDataURL('image/png').split(',')[1];
                    images.push({
                        pageNumber: startPage + pageNum - 1, // Adjust page number to original document
                        data: imageData
                    });

                } catch (error) {
                    console.error(`Error converting page ${pageNum} to image:`, error);
                }
            }
        }
    } catch (error) {
        console.error('Error loading PDF chunk:', error);
    }

    return images;
}

async function extractTextWithGemini(pageImages, startPage, endPage) {
    try {
        document.getElementById('currentStatus').textContent = 'Sending images to Gemini AI...';

        // Prepare the parts for Gemini API
        const parts = [
            {
                text: `Analyse and Get the following information from the file:

                IMPORTANT: You must return ONLY valid JSON format. Do not include any markdown, headers, or other text outside the JSON.

                For each page from pages ${startPage}-${endPage}, analyze and extract the following specific information:

                1. Content_type (certificate, CV, police clearance, letter, reference, ids etc...)
                2. Content_date (important dates in the content of the file)
                3. Content_elements (What are in the content - tables, images, graphs, charts, hand written, stamp, signatures, etc...)
                4. Content_brief (the brief about the content, what is the content about)
                5. Content_additional_elements (eg. stamps - describe the stamp, signatures, dates etc...)

                One file can contain multiple content types, base on content types and analyse and write the above information.

                Return ONLY this JSON structure (no other text):
                {
                  "pages": [
                    {
                      "page_number": 1,
                      "content_type": "certificate, CV, police clearance, letter, reference, ids etc...",
                      "content_date": "important dates found in the content",
                      "content_elements": ["tables", "images", "graphs", "charts", "handwritten", "stamps", "signatures", "etc"],
                      "content_brief": "brief description of what the content is about",
                      "content_additional_elements": "detailed description of stamps, signatures, dates, and other additional elements found"
                    }
                  ]
                }

                Process all ${pageImages.length} pages completely. Focus only on extracting the specific information requested above.`
            }
        ];

        // Add each page image
        pageImages.forEach(image => {
            parts.push({
                inline_data: {
                    mime_type: "image/png",
                    data: image.data
                }
            });
        });

        const requestBody = {
            contents: [{
                parts: parts
            }],
            generationConfig: {
                temperature: 0.3, // Optimized for creative text rewriting of complex layouts
                maxOutputTokens: 65536, // Increased for better text extraction
                topP: 0.9,
                topK: 40,
                candidateCount: 1, // Single focused response
                stopSequences: ["[REPETITION_DETECTED]"] // Stop if repetitive patterns detected
            }
        };

        document.getElementById('currentStatus').textContent = 'AI analyzing and processing content...';

        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.candidates && data.candidates.length > 0 && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
            let extractedText = data.candidates[0].content.parts[0].text;

            // Try to extract JSON from the response if it contains extra text
            try {
                // Look for JSON structure in the response
                const jsonMatch = extractedText.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const jsonText = jsonMatch[0];
                    // Validate it's proper JSON
                    JSON.parse(jsonText);
                    extractedText = jsonText;
                }
            } catch (jsonError) {
                console.warn('Response is not valid JSON, using as-is:', jsonError);
            }

            return extractedText;
        } else {
            console.error('Invalid API response structure:', data);
            throw new Error('No text content returned from Gemini API');
        }

    } catch (error) {
        console.error(`Error processing pages ${startPage}-${endPage}:`, error);
        return `\n\n**Error processing pages ${startPage}-${endPage}: ${error.message}**\n\n`;
    }
}

async function generateTitleAndDescription() {
    try {
        // Parse the extracted JSON to get content types and brief descriptions
        let contentTypes = [];
        let contentBriefs = [];

        try {
            const jsonData = JSON.parse(extractedText.replace(/^.*?## Pages \d+-\d+\s*/, ''));
            if (jsonData.pages && Array.isArray(jsonData.pages)) {
                jsonData.pages.forEach(page => {
                    if (page.content_type) {
                        contentTypes.push(page.content_type);
                    }
                    if (page.content_brief) {
                        contentBriefs.push(page.content_brief);
                    }
                });
            }
        } catch (parseError) {
            console.warn('Could not parse JSON for title generation, using fallback');
        }

        // Generate title and description based on content analysis
        let title = '';
        let description = '';

        if (contentTypes.length > 0) {
            // Use the most common content type for title
            const mainContentType = contentTypes[0];
            title = mainContentType.charAt(0).toUpperCase() + mainContentType.slice(1);
            if (title.length > 50) {
                title = title.substring(0, 47) + '...';
            }
        } else {
            title = selectedFile.name.replace('.pdf', '');
        }

        if (contentBriefs.length > 0) {
            // Combine content briefs for description
            description = contentBriefs.join('. ');
            if (description.length > 200) {
                description = description.substring(0, 197) + '...';
            }
        } else {
            description = 'Document analyzed with AI content extraction';
        }

        // Set the generated values
        document.querySelector('input[name="file_title"]').value = title;
        document.querySelector('textarea[name="file_description"]').value = description;

    } catch (error) {
        console.error('Error generating title and description:', error);
        // Set default values
        document.querySelector('input[name="file_title"]').value = selectedFile.name.replace('.pdf', '');
        document.querySelector('textarea[name="file_description"]').value = 'PDF document with AI content analysis';
    }
}

// Timing functions

function startTimingUpdates() {
    timingInterval = setInterval(updateTimingDisplay, 1000);
}

function stopTimingUpdates() {
    if (timingInterval) {
        clearInterval(timingInterval);
        timingInterval = null;
    }
}

function updateTimingDisplay() {
    if (!processingStartTime) return;

    const elapsed = Date.now() - processingStartTime;
    document.getElementById('elapsedTime').textContent = formatTime(elapsed);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showError(message) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-danger, .alert-success');
    existingMessages.forEach(msg => {
        if (msg.classList.contains('alert-danger') || msg.classList.contains('alert-success')) {
            msg.remove();
        }
    });

    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger alert-dismissible fade show';
    errorDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.card-body').insertBefore(errorDiv, document.querySelector('.card-body').firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-danger, .alert-success');
    existingMessages.forEach(msg => {
        if (msg.classList.contains('alert-danger') || msg.classList.contains('alert-success')) {
            msg.remove();
        }
    });

    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success alert-dismissible fade show';
    successDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.card-body').insertBefore(successDiv, document.querySelector('.card-body').firstChild);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.remove();
        }
    }, 3000);
}

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

async function validateExtractedText(extractedText) {
    try {
        // Quick validation checks first
        const quickValidation = performQuickValidation(extractedText);

        if (quickValidation.isValid) {
            return {
                isValid: true,
                issues: [],
                problematicSets: [],
                shouldRetry: false
            };
        }

        // If quick validation fails, use AI for detailed analysis
        const aiValidationPrompt = `Please check this extracted text from a PDF document for any problems or errors. I have the authority to review this information as we are conducting a recruitment exercise.

        Look for these issues:
        - Garbled or nonsensical text
        - Broken words or incomplete sentences
        - Too much repetition
        - Missing content
        - Broken tables or formatting
        - Text that doesn't make sense

        Return a JSON object with:
        {
            "hasIssues": true/false,
            "severity": "low/medium/high",
            "issues": ["list of specific issues found"],
            "problematicSections": ["list of page ranges with issues"],
            "recommendRetry": true/false,
            "overallQuality": "poor/fair/good/excellent"
        }

        TEXT TO CHECK:
        ${extractedText.substring(0, 8000)}${extractedText.length > 8000 ? '\n\n[Content truncated for analysis]' : ''}`;

        const requestBody = {
            contents: [{
                parts: [{
                    text: aiValidationPrompt
                }]
            }],
            generationConfig: {
                temperature: 0.1,
                maxOutputTokens: 1024
            }
        };

        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            console.error('AI validation failed, using quick validation results');
            return quickValidation;
        }

        const data = await response.json();

        if (data.candidates && data.candidates.length > 0 && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
            const aiResponse = data.candidates[0].content.parts[0].text;

            try {
                // Try to parse JSON response
                const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    const analysis = JSON.parse(jsonMatch[0]);

                    return {
                        isValid: !analysis.hasIssues || analysis.severity === 'low',
                        issues: analysis.issues || [],
                        problematicSets: extractProblematicSets(analysis.problematicSections || []),
                        shouldRetry: analysis.recommendRetry && analysis.severity !== 'low'
                    };
                }
            } catch (parseError) {
                console.error('Failed to parse AI validation response:', parseError);
            }
        }

        // Fallback to quick validation if AI analysis fails
        return quickValidation;

    } catch (error) {
        console.error('Error validating extracted text:', error);
        // Return permissive result if validation fails
        return {
            isValid: true,
            issues: ['Validation process failed'],
            problematicSets: [],
            shouldRetry: false
        };
    }
}

function performQuickValidation(text) {
    const issues = [];
    let shouldRetry = false;

    // Check for minimum content length
    if (text.length < 100) {
        issues.push('Extracted text is too short');
        shouldRetry = true;
    }

    // Check for excessive repetition
    const repetitionPattern = /(.{10,})\1{3,}/g;
    if (repetitionPattern.test(text)) {
        issues.push('Excessive text repetition detected');
        shouldRetry = true;
    }

    // Check for garbled text (too many special characters)
    const specialCharRatio = (text.match(/[^\w\s\.\,\!\?\-\(\)\[\]]/g) || []).length / text.length;
    if (specialCharRatio > 0.1) {
        issues.push('High ratio of special characters (possible OCR errors)');
        shouldRetry = true;
    }

    // Check for incomplete extraction indicators
    if (text.includes('...') || text.includes('[truncated]') || text.includes('Error processing')) {
        issues.push('Incomplete or error indicators found');
        shouldRetry = true;
    }

    // Check for very short sections (might indicate extraction failure)
    const sections = text.split('## Pages');
    const shortSections = sections.filter(section => section.trim().length < 50);
    if (shortSections.length > sections.length * 0.3) {
        issues.push('Multiple sections with very little content');
        shouldRetry = true;
    }

    return {
        isValid: issues.length === 0 || !shouldRetry,
        issues: issues,
        problematicSets: [],
        shouldRetry: shouldRetry
    };
}

function extractProblematicSets(problematicSections) {
    const sets = [];

    problematicSections.forEach(section => {
        // Extract page ranges like "Pages 1-5" or "Set 2"
        const pageMatch = section.match(/Pages?\s+(\d+)(?:-(\d+))?/i);
        const setMatch = section.match(/Set\s+(\d+)/i);

        if (pageMatch) {
            const startPage = parseInt(pageMatch[1]);
            const endPage = pageMatch[2] ? parseInt(pageMatch[2]) : startPage;
            // Convert page range to set number (5 pages per set)
            const setNumber = Math.ceil(startPage / 5);
            if (!sets.includes(setNumber)) {
                sets.push(setNumber);
            }
        } else if (setMatch) {
            const setNumber = parseInt(setMatch[1]);
            if (!sets.includes(setNumber)) {
                sets.push(setNumber);
            }
        }
    });

    return sets;
}





// Clear previous file data when new file is uploaded
function clearPreviousFileData() {
    // Clear extracted text
    extractedText = '';

    // Clear hidden form fields
    document.getElementById('hiddenExtractedText').value = '';
    document.getElementById('hiddenFileData').value = '';

    // Clear file title and description
    const fileTitleInput = document.querySelector('input[name="file_title"]');
    const fileDescInput = document.querySelector('textarea[name="file_description"]');
    if (fileTitleInput) {
        fileTitleInput.value = '';
        fileTitleInput.classList.remove('is-valid', 'is-invalid');
    }
    if (fileDescInput) {
        fileDescInput.value = '';
        fileDescInput.classList.remove('is-valid', 'is-invalid');
    }

    // Clear extracted text display
    const extractedTextContent = document.getElementById('extractedTextContent');
    if (extractedTextContent) {
        extractedTextContent.textContent = '';
    }

    // Hide sections that should be hidden for new file
    document.getElementById('titleDescriptionSection').style.display = 'none';
    document.getElementById('extractedTextSection').style.display = 'none';
    document.getElementById('fileInfo').style.display = 'none';
    document.getElementById('progressSection').style.display = 'none';

    // Reset processing flags
    allPagesConverted = false;
    convertedPageSets = [];

    // Reset button state
    const processBtn = document.getElementById('processUploadBtn');
    if (processBtn) {
        processBtn.disabled = false;
        processBtn.innerHTML = '<i class="fas fa-cogs me-2"></i>Process and Upload';
    }

    console.log('Previous file data cleared for new upload');
}

// Status update functions
function resetProcessingStatus() {
    const statusItems = ['split', 'image', 'ai', 'upload'];
    const statusTexts = [
        'PDF Splitting: Pending',
        'AI Readable Format: Pending',
        'AI Document Analysis: Pending',
        'File Upload: Pending'
    ];

    statusItems.forEach((item, index) => {
        const statusElement = document.getElementById(`${item}Status`);
        const iconElement = document.getElementById(`${item}Icon`);
        const containerElement = statusElement.parentElement;

        if (statusElement) statusElement.textContent = statusTexts[index];
        if (iconElement) {
            iconElement.className = `fas fa-${getIconClass(item)} me-2 text-muted`;
        }
        if (containerElement) {
            containerElement.classList.remove('processing', 'completed');
        }
    });
}

function updateProcessingStatus(type, status, text) {
    const statusElement = document.getElementById(`${type}Status`);
    const iconElement = document.getElementById(`${type}Icon`);
    const containerElement = statusElement.parentElement;

    if (statusElement) {
        statusElement.textContent = text;
    }

    if (containerElement) {
        containerElement.classList.remove('processing', 'completed');
        containerElement.classList.add(status);
    }

    if (iconElement && status === 'completed') {
        iconElement.className = `fas fa-check-circle me-2 text-success`;
    } else if (iconElement && status === 'processing') {
        iconElement.className = `fas fa-spinner fa-spin me-2 text-danger`;
    }
}

function getIconClass(type) {
    const icons = {
        'split': 'cut',
        'image': 'cogs',
        'ai': 'robot',
        'upload': 'upload',
        'complete': 'check-circle'
    };
    return icons[type] || 'circle';
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Page initialization if needed
});
</script>
<?= $this->endSection() ?>

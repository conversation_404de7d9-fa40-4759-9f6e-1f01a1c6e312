<?= $this->extend('templates/home_template') ?>

<?= $this->section('content') ?>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Create Account</h3>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    <?php endif; ?>

                    <?= form_open('applicant/register') ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="firstname" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="firstname" name="firstname"
                                       value="<?= old('firstname') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lastname" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="lastname" name="lastname"
                                       value="<?= old('lastname') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?= old('email') ?>" required>
                            <span class="input-group-text" id="email-status">
                                <i class="fas fa-question-circle text-muted" id="email-icon"></i>
                            </span>
                        </div>
                        <div id="email-feedback" class="form-text"></div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <small class="form-text text-muted">Minimum 4 characters</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-red" id="submit-btn" disabled>Create Account</button>
                    </div>

                    <?= form_close() ?>

                    <div class="text-center mt-3">
                        <p>Already have an account? <a href="<?= base_url('applicant/login') ?>" class="text-decoration-none">Login here</a></p>
                        <a href="<?= base_url('/') ?>" class="text-decoration-none">Back to Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    let emailCheckTimeout;
    let isEmailValid = false;

    // Email input event handler
    $('#email').on('input', function() {
        const email = $(this).val().trim();
        const emailIcon = $('#email-icon');
        const emailFeedback = $('#email-feedback');
        const submitBtn = $('#submit-btn');

        // Clear previous timeout
        clearTimeout(emailCheckTimeout);

        // Reset states
        isEmailValid = false;
        submitBtn.prop('disabled', true);

        if (email === '') {
            emailIcon.removeClass().addClass('fas fa-question-circle text-muted');
            emailFeedback.text('').removeClass();
            return;
        }

        // Basic email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            emailIcon.removeClass().addClass('fas fa-times-circle text-danger');
            emailFeedback.text('Please enter a valid email address').removeClass().addClass('text-danger');
            return;
        }

        // Show checking state
        emailIcon.removeClass().addClass('fas fa-spinner fa-spin text-primary');
        emailFeedback.text('Checking email availability...').removeClass().addClass('text-primary');

        // Debounce the AJAX call
        emailCheckTimeout = setTimeout(function() {
            checkEmailAvailability(email);
        }, 800);
    });

    // Function to check email availability via AJAX
    function checkEmailAvailability(email) {
        $.ajax({
            url: '<?= base_url('applicant/check-email') ?>',
            type: 'POST',
            data: {
                email: email,
                <?= csrf_token() ?>: '<?= csrf_hash() ?>'
            },
            dataType: 'json',
            success: function(response) {
                const emailIcon = $('#email-icon');
                const emailFeedback = $('#email-feedback');
                const submitBtn = $('#submit-btn');

                if (response.success && response.available) {
                    // Email is available
                    emailIcon.removeClass().addClass('fas fa-check-circle text-success');
                    emailFeedback.text('Email address is available').removeClass().addClass('text-success');
                    isEmailValid = true;
                    checkFormValidity();
                } else {
                    // Email is not available or error
                    emailIcon.removeClass().addClass('fas fa-times-circle text-danger');
                    emailFeedback.text(response.message || 'Email address is not available').removeClass().addClass('text-danger');
                    isEmailValid = false;
                    submitBtn.prop('disabled', true);
                }
            },
            error: function(xhr, status, error) {
                const emailIcon = $('#email-icon');
                const emailFeedback = $('#email-feedback');
                const submitBtn = $('#submit-btn');

                emailIcon.removeClass().addClass('fas fa-exclamation-triangle text-warning');
                emailFeedback.text('Error checking email availability. Please try again.').removeClass().addClass('text-warning');
                isEmailValid = false;
                submitBtn.prop('disabled', true);

                console.error('Email check error:', error);
            }
        });
    }

    // Function to check overall form validity
    function checkFormValidity() {
        const firstname = $('#firstname').val().trim();
        const lastname = $('#lastname').val().trim();
        const email = $('#email').val().trim();
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();
        const submitBtn = $('#submit-btn');

        // Check if all fields are filled and email is valid
        if (firstname && lastname && email && password && confirmPassword &&
            password === confirmPassword && password.length >= 4 && isEmailValid) {
            submitBtn.prop('disabled', false);
        } else {
            submitBtn.prop('disabled', true);
        }
    }

    // Monitor other form fields for validation
    $('#firstname, #lastname, #password, #confirm_password').on('input', function() {
        checkFormValidity();
    });

    // Password confirmation validation
    $('#confirm_password').on('input', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();

        if (confirmPassword && password !== confirmPassword) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }

        checkFormValidity();
    });

    // Form submission handler
    $('form').on('submit', function(e) {
        if (!isEmailValid) {
            e.preventDefault();
            toastr.error('Please ensure the email address is valid and available.');
            return false;
        }

        // Show loading state
        const submitBtn = $('#submit-btn');
        const originalText = submitBtn.text();
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...').prop('disabled', true);

        // Re-enable button after 5 seconds in case of slow response
        setTimeout(function() {
            submitBtn.html(originalText).prop('disabled', false);
        }, 5000);
    });
});
</script>
<?= $this->endSection() ?>

-- Migration script to update existing position_views table to support GPS coordinates
-- Run this if you already have the old province-based table structure

-- First, check if the table exists and what columns it has
-- SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'position_views';

-- Add GPS coordinates column
ALTER TABLE `position_views`
ADD COLUMN `gps_coordinates` varchar(50) DEFAULT NULL COMMENT 'GPS coordinates in format: latitude,longitude' AFTER `viewer_ip`;

-- Add index for the GPS coordinates column
ALTER TABLE `position_views`
ADD KEY `idx_gps_coordinates` (`gps_coordinates`);

-- Optional: If you want to remove the old province_id column (only if you're sure you don't need it)
-- ALTER TABLE `position_views` DROP FOREIGN KEY `fk_position_views_province_id`;
-- ALTER TABLE `position_views` DROP COLUMN `province_id`;
-- ALTER TABLE `position_views` DROP KEY `idx_province_id`;

-- Sample data migration (if you have existing province data and want to convert it to GPS coordinates)
-- This is just an example - you would need to customize this based on your actual data
/*
UPDATE position_views pv
JOIN geo_provinces gp ON pv.province_id = gp.id
SET
    pv.gps_coordinates = CASE
        WHEN gp.name = 'National Capital District' THEN '-9.4438,147.1803'
        WHEN gp.name = 'Western' THEN '-8.0000,142.0000'
        WHEN gp.name = 'Gulf' THEN '-7.5000,144.0000'
        WHEN gp.name = 'Central' THEN '-9.0000,147.0000'
        WHEN gp.name = 'Milne Bay' THEN '-10.5000,150.5000'
        WHEN gp.name = 'Northern' THEN '-8.5000,148.0000'
        WHEN gp.name = 'Southern Highlands' THEN '-6.0000,143.0000'
        WHEN gp.name = 'Western Highlands' THEN '-5.5000,144.0000'
        WHEN gp.name = 'Enga' THEN '-5.5000,143.5000'
        WHEN gp.name = 'Chimbu' THEN '-6.0000,145.0000'
        WHEN gp.name = 'Eastern Highlands' THEN '-6.5000,145.5000'
        WHEN gp.name = 'Morobe' THEN '-7.0000,147.0000'
        WHEN gp.name = 'Madang' THEN '-5.2000,145.8000'
        WHEN gp.name = 'East Sepik' THEN '-4.0000,143.0000'
        WHEN gp.name = 'West Sepik' THEN '-4.5000,141.0000'
        WHEN gp.name = 'Manus' THEN '-2.0000,147.0000'
        WHEN gp.name = 'New Ireland' THEN '-3.0000,152.0000'
        WHEN gp.name = 'East New Britain' THEN '-4.5000,152.0000'
        WHEN gp.name = 'West New Britain' THEN '-5.5000,150.0000'
        WHEN gp.name = 'Bougainville' THEN '-6.0000,155.0000'
        ELSE NULL
    END
WHERE pv.province_id IS NOT NULL;
*/

-- Verify the changes
SELECT
    COUNT(*) as total_records,
    COUNT(gps_coordinates) as records_with_coordinates,
    COUNT(DISTINCT gps_coordinates) as unique_locations
FROM position_views;

-- Show sample data
SELECT
    position_id,
    viewer_ip,
    gps_coordinates,
    viewed_at
FROM position_views
WHERE gps_coordinates IS NOT NULL
LIMIT 10;

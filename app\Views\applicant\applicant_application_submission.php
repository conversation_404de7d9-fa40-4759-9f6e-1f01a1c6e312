<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="<?= base_url('applicant/jobs/position/' . $position['id']) ?>" class="btn btn-outline-primary mb-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Position Details
                    </a>
                    <h1 class="h3 mb-2">
                        Application Submission
                        <span class="badge bg-primary ms-2"><?= esc($position['position_reference']) ?></span>
                    </h1>
                    <p class="text-muted">
                        <strong>Position:</strong> <?= esc($position['designation']) ?><br>
                        <strong>Organization:</strong> <?= esc($organization['org_name']) ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Submission Process -->
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Process Steps -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ol me-2"></i>Application Processing Steps
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <div id="processSteps">
                                <div class="step-item" id="step1">
                                    <span class="step-number">1.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Profile Information: Collected from your profile</span>
                                </div>
                                <div class="step-item" id="step2">
                                    <span class="step-number">2.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Education Records: Retrieved from your education history</span>
                                </div>
                                <div class="step-item" id="step3">
                                    <span class="step-number">3.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Work Experience: Collected from your experience records</span>
                                </div>
                                <div class="step-item" id="step4">
                                    <span class="step-number">4.</span>
                                    <span class="step-icon"><i class="fas fa-check text-success"></i></span>
                                    <span class="step-text">Supporting Documents: Retrieved from your uploaded files</span>
                                </div>
                                <div class="step-item" id="step5">
                                    <span class="step-number">5.</span>
                                    <span class="step-icon"><i class="fas fa-clock text-warning"></i></span>
                                    <span class="step-text">Profile Generation: Generate comprehensive applicant profile</span>
                                </div>
                                <div class="step-item" id="step6">
                                    <span class="step-number">6.</span>
                                    <span class="step-icon"><i class="fas fa-clock text-muted"></i></span>
                                    <span class="step-text">Final Submission: Submit application to organization</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Section -->
            <div class="card mb-4" id="analysisSection">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>Profile Generation
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div id="analysisButton">
                        <p class="text-muted mb-4">
                            Click the button below to generate your comprehensive application profile from your stored data.
                        </p>
                        <button type="button" class="btn btn-primary btn-lg" id="analyzeProfileBtn">
                            <i class="fas fa-cogs me-2"></i>Generate and Process My Application
                        </button>
                    </div>

                    <!-- Processing Status -->
                    <div id="processingStatus" style="display: none;">
                        <div class="mb-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <h6 id="processingTitle">Processing Your Application...</h6>
                        <p id="processingMessage" class="text-muted">Collecting your profile information...</p>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" 
                                 style="width: 0%" 
                                 id="processingProgress">
                            </div>
                        </div>
                    </div>

                    <!-- Analysis Results -->
                    <div id="analysisResults" style="display: none;">
                        <div class="alert alert-success">
                            <h6 class="alert-heading">
                                <i class="fas fa-check-circle me-2"></i>Profile Generation Complete
                            </h6>
                            <p class="mb-0">Your comprehensive application profile has been generated successfully.</p>
                        </div>

                        <!-- Profile Preview -->
                        <div class="text-start">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Generated Profile Summary</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="toggleProfileBtn">
                                    <i class="fas fa-eye me-1"></i>View Profile
                                </button>
                            </div>
                            
                            <div id="profilePreview" style="display: none;">
                                <div class="border rounded p-3 bg-light">
                                    <pre id="profileContent" class="mb-0" style="white-space: pre-wrap; font-size: 0.9em; max-height: 400px; overflow-y: auto;"></pre>
                                </div>
                            </div>
                        </div>

                        <!-- Final Submission Button -->
                        <div class="mt-4">
                            <button type="button" class="btn btn-success btn-lg" id="finalSubmitBtn">
                                <i class="fas fa-paper-plane me-2"></i>Your Application is Ready to be Submitted
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for final submission -->
<form id="finalSubmissionForm" action="<?= base_url('applicant/jobs/final-submission/' . $position['id']) ?>" method="post" style="display: none;">
    <?= csrf_field() ?>
    <input type="hidden" name="profile_data" id="hiddenProfileData">
</form>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.step-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.step-item:last-child {
    border-bottom: none;
}

.step-number {
    font-weight: bold;
    margin-right: 10px;
    min-width: 20px;
}

.step-icon {
    margin-right: 10px;
    min-width: 20px;
}

.step-text {
    flex: 1;
}

.progress {
    height: 8px;
}

#profileContent {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.swal2-popup-large {
    width: 500px !important;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
let generatedProfile = null;

$(document).ready(function() {
    // Handle analyze profile button
    $('#analyzeProfileBtn').on('click', function() {
        analyzeProfile();
    });

    // Handle toggle profile view
    $('#toggleProfileBtn').on('click', function() {
        const preview = $('#profilePreview');
        const btn = $(this);
        
        if (preview.is(':visible')) {
            preview.hide();
            btn.html('<i class="fas fa-eye me-1"></i>View Profile');
        } else {
            preview.show();
            btn.html('<i class="fas fa-eye-slash me-1"></i>Hide Profile');
        }
    });

    // Handle final submission
    $('#finalSubmitBtn').on('click', function() {
        if (generatedProfile) {
            // Show SweetAlert confirmation
            Swal.fire({
                title: 'Submit Application',
                html: `
                    <div class="text-start">
                        <p><strong>Position:</strong> <?= esc($position['designation']) ?></p>
                        <p><strong>Organization:</strong> <?= esc($organization['org_name']) ?></p>
                        <hr>
                        <p class="text-muted">Are you sure you want to submit your application for this position?</p>
                        <p class="text-warning"><small><i class="fas fa-exclamation-triangle me-1"></i>This action cannot be undone once submitted.</small></p>
                        <p class="text-info"><small><i class="fas fa-info-circle me-1"></i>Your application has been processed and analyzed using AI to create a comprehensive profile.</small></p>
                    </div>
                `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#198754',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '<i class="fas fa-check me-2"></i>Yes, Submit Application',
                cancelButtonText: '<i class="fas fa-times me-2"></i>Cancel',
                reverseButtons: true,
                customClass: {
                    popup: 'swal2-popup-large'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    $('#hiddenProfileData').val(JSON.stringify(generatedProfile));
                    // Submit the form normally (not via AJAX to avoid CSRF issues)
                    $('#finalSubmissionForm')[0].submit();
                }
            });
        }
    });
});

async function analyzeProfile() {
    try {
        // Hide analyze button and show processing
        $('#analysisButton').hide();
        $('#processingStatus').show();

        // Update step 5 to processing
        updateStepStatus(5, 'processing', 'Profile Generation: Starting generation...');

        // Step 1: Collect applicant data
        updateProcessingStatus('Collecting your profile information...', 20);
        const applicantData = await collectApplicantData();

        // Step 2: Generate structured profile from collected data
        updateProcessingStatus('Generating comprehensive profile structure...', 60);
        const profileAnalysis = generateStructuredProfile(applicantData.data);

        // Step 3: Complete
        updateProcessingStatus('Profile generation complete!', 100);
        generatedProfile = profileAnalysis;

        // Update step 5 to complete
        updateStepStatus(5, 'complete', 'Profile Generation: Comprehensive profile generated');
        updateStepStatus(6, 'ready', 'Final Submission: Ready to submit application');

        // Show results
        setTimeout(() => {
            $('#processingStatus').hide();
            $('#profileContent').text(JSON.stringify(profileAnalysis, null, 2));
            $('#analysisResults').show();
        }, 1000);

    } catch (error) {
        console.error('Error generating profile:', error);
        updateStepStatus(5, 'error', 'Profile Generation: Error occurred during generation');
        alert('Error generating profile: ' + error.message);

        // Reset UI
        $('#processingStatus').hide();
        $('#analysisButton').show();
    }
}

async function collectApplicantData() {
    const response = await fetch('<?= base_url('applicant/jobs/process-application/' . $position['id']) ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
        })
    });

    if (!response.ok) {
        throw new Error('Failed to collect applicant data');
    }

    return await response.json();
}

function generateStructuredProfile(applicantData) {
    // Generate comprehensive profile structure from collected data
    const profile = {
        analysis_type: "comprehensive_profile",
        generated_at: new Date().toISOString(),
        generation_method: "code_based",

        personal_information: {
            basic_details: applicantData.personal_info || {},
            contact_information: {
                email: applicantData.personal_info?.email || '',
                phone: applicantData.personal_info?.phone || '',
                address: applicantData.personal_info?.current_address || ''
            },
            identification: {
                national_id: applicantData.personal_info?.national_id || '',
                passport_number: applicantData.personal_info?.passport_number || '',
                driving_license: applicantData.personal_info?.driving_license || ''
            },
            demographics: {
                gender: applicantData.personal_info?.gender || '',
                date_of_birth: applicantData.personal_info?.date_of_birth || '',
                place_of_origin: applicantData.personal_info?.place_of_origin || '',
                citizenship: applicantData.personal_info?.citizenship || ''
            }
        },

        employment_information: {
            current_employment: {
                employer: applicantData.personal_info?.current_employer || '',
                position: applicantData.personal_info?.current_position || '',
                salary: applicantData.personal_info?.current_salary || '',
                is_public_servant: applicantData.personal_info?.is_public_servant || false,
                public_service_file_number: applicantData.personal_info?.public_service_file_number || '',
                employee_of_org_id: applicantData.personal_info?.employee_of_org_id || null
            },
            work_experience: applicantData.experiences || []
        },

        educational_background: {
            education_records: applicantData.education || [],
            qualifications_summary: generateEducationSummary(applicantData.education || [])
        },

        supporting_documents: {
            uploaded_files: applicantData.files || [],
            document_analysis: generateDocumentAnalysis(applicantData.files || [])
        },

        referees_information: applicantData.referees || [],

        profile_completeness: calculateProfileCompleteness(applicantData),

        application_summary: generateApplicationSummary(applicantData)
    };

    return profile;
}

function generateEducationSummary(education) {
    if (!education || education.length === 0) {
        return "No education records found";
    }

    const summary = {
        total_qualifications: education.length,
        highest_level: getHighestEducationLevel(education),
        institutions: education.map(edu => edu.institution_name).filter(Boolean),
        fields_of_study: education.map(edu => edu.field_of_study).filter(Boolean)
    };

    return summary;
}

function getHighestEducationLevel(education) {
    // Simple priority mapping - adjust based on your education levels
    const levelPriority = {
        'PhD': 8,
        'Masters': 7,
        'Bachelor': 6,
        'Diploma': 5,
        'Certificate': 4,
        'Grade 12': 3,
        'Grade 10': 2
    };

    let highest = '';
    let highestPriority = 0;

    education.forEach(edu => {
        const level = edu.education_level || '';
        const priority = levelPriority[level] || 1;
        if (priority > highestPriority) {
            highestPriority = priority;
            highest = level;
        }
    });

    return highest || 'Not specified';
}

function generateDocumentAnalysis(files) {
    if (!files || files.length === 0) {
        return "No documents uploaded";
    }

    const analysis = {
        total_documents: files.length,
        document_types: files.map(file => file.file_title).filter(Boolean),
        has_extracted_text: files.filter(file => file.file_extracted_texts && file.file_extracted_texts.trim()).length,
        total_text_content: files.reduce((total, file) => {
            return total + (file.file_extracted_texts ? file.file_extracted_texts.length : 0);
        }, 0)
    };

    return analysis;
}

function calculateProfileCompleteness(applicantData) {
    const requiredFields = [
        'personal_info.name',
        'personal_info.email',
        'personal_info.phone',
        'personal_info.gender',
        'personal_info.date_of_birth',
        'personal_info.current_address',
        'personal_info.place_of_origin',
        'personal_info.citizenship'
    ];

    let completedFields = 0;

    requiredFields.forEach(field => {
        const fieldParts = field.split('.');
        let value = applicantData;

        for (const part of fieldParts) {
            value = value?.[part];
        }

        if (value && value.toString().trim()) {
            completedFields++;
        }
    });

    const percentage = Math.round((completedFields / requiredFields.length) * 100);

    return {
        completed_fields: completedFields,
        total_required_fields: requiredFields.length,
        completion_percentage: percentage,
        status: percentage >= 80 ? 'Complete' : percentage >= 60 ? 'Mostly Complete' : 'Incomplete'
    };
}

function generateApplicationSummary(applicantData) {
    const personal = applicantData.personal_info || {};
    const education = applicantData.education || [];
    const experience = applicantData.experiences || [];
    const files = applicantData.files || [];

    return {
        applicant_name: personal.name || 'Not provided',
        total_education_records: education.length,
        total_work_experience: experience.length,
        total_supporting_documents: files.length,
        current_employment_status: personal.current_employer ? 'Employed' : 'Not specified',
        public_servant_status: personal.is_public_servant ? 'Yes' : 'No',
        profile_strength: calculateProfileStrength(applicantData)
    };
}

function calculateProfileStrength(applicantData) {
    let score = 0;

    // Personal information completeness (30 points)
    const completeness = calculateProfileCompleteness(applicantData);
    score += (completeness.completion_percentage / 100) * 30;

    // Education records (25 points)
    const education = applicantData.education || [];
    if (education.length > 0) score += 15;
    if (education.length > 2) score += 10;

    // Work experience (25 points)
    const experience = applicantData.experiences || [];
    if (experience.length > 0) score += 15;
    if (experience.length > 2) score += 10;

    // Supporting documents (20 points)
    const files = applicantData.files || [];
    if (files.length > 0) score += 10;
    if (files.length > 3) score += 10;

    const percentage = Math.round(score);

    if (percentage >= 80) return 'Excellent';
    if (percentage >= 60) return 'Good';
    if (percentage >= 40) return 'Fair';
    return 'Needs Improvement';
}



function updateProcessingStatus(message, progress) {
    $('#processingMessage').text(message);
    $('#processingProgress').css('width', progress + '%');
}

function updateStepStatus(stepNumber, status, text) {
    const step = $(`#step${stepNumber}`);
    const icon = step.find('.step-icon i');

    // Reset classes
    icon.removeClass('fas fa-check text-success fa-clock text-warning fa-times text-danger fa-spinner fa-spin text-primary text-muted');

    switch (status) {
        case 'complete':
            icon.addClass('fas fa-check text-success');
            break;
        case 'processing':
            icon.addClass('fas fa-spinner fa-spin text-primary');
            break;
        case 'ready':
            icon.addClass('fas fa-clock text-warning');
            break;
        case 'error':
            icon.addClass('fas fa-times text-danger');
            break;
        default:
            icon.addClass('fas fa-clock text-muted');
    }

    if (text) {
        step.find('.step-text').text(text);
    }
}
</script>
<?= $this->endSection() ?>

# Pre-Screening Criteria JSON Structure

## Overview
This document defines the standard JSON structure for pre-screening criteria in the DERS (Dakoii Echad Recruitment & Selection System). The pre-screening criteria are stored in the `exercises.pre_screen_criteria` field as JSON text.

## Database Context
- **Table**: `exercises`
- **Field**: `pre_screen_criteria` (TEXT)
- **Storage Format**: JSON string
- **Related Models**: `ExerciseModel.php`
- **Controllers**: `ExercisesController.php`, `ApplicationPreScreeningController.php`

## Basic Structure

### Individual Criteria Item
```json
{
  "name": "string (required)",
  "description": "string (optional)"
}
```

### Complete Criteria Array
```json
[
  {
    "name": "Criteria Name 1",
    "description": "Detailed description of the criteria"
  },
  {
    "name": "Criteria Name 2", 
    "description": "Another criteria description"
  }
]
```

## Field Specifications

### Required Fields
- **name** (string): The title/name of the pre-screening criteria
  - Must not be empty
  - Should be descriptive and clear
  - Maximum recommended length: 255 characters

### Optional Fields
- **description** (string): Detailed explanation of the criteria
  - Can be empty or null
  - Provides additional context for applicants and reviewers
  - No length limit but should be reasonable

## Real-World Examples

### Example 1: Document Requirements
```json
[
  {
    "name": "The RS 3.2 must be signed",
    "description": "No description provided"
  },
  {
    "name": "Must have RS 3.2 Application form",
    "description": "No description provided"
  },
  {
    "name": "All academic papers must be stamped by the commissioner of oaths",
    "description": "No description provided"
  }
]
```

### Example 2: Age and Experience Requirements
```json
[
  {
    "name": "Must be less than 65 Years of age",
    "description": "Applicants must not exceed the mandatory retirement age"
  },
  {
    "name": "Minimum 5 years relevant experience",
    "description": "Must demonstrate at least 5 years of experience in the relevant field"
  },
  {
    "name": "Valid professional certification",
    "description": "Must hold current professional certification relevant to the position"
  }
]
```

### Example 3: Educational Requirements
```json
[
  {
    "name": "Bachelor's degree in relevant field",
    "description": "Must hold a recognized bachelor's degree from an accredited institution"
  },
  {
    "name": "English proficiency certification",
    "description": "IELTS score of 6.5 or equivalent English language certification"
  },
  {
    "name": "Computer literacy certificate",
    "description": "Must provide evidence of basic computer skills certification"
  }
]
```

## Empty State
When no criteria are defined:
```json
[]
```

## Validation Rules

### JSON Structure Validation
- Must be valid JSON format
- Must be an array at the root level
- Each array item must be an object
- Each object must contain at least the "name" field

### Field Validation
- **name**: Required, non-empty string
- **description**: Optional string, can be empty or null

### CodeIgniter 4 Implementation
```php
// Validation in controller
if ($criteria) {
    try {
        $decoded = json_decode($criteria, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON format');
        }
        // Additional validation for structure
        foreach ($decoded as $item) {
            if (!isset($item['name']) || empty(trim($item['name']))) {
                throw new Exception('Each criteria must have a name');
            }
        }
    } catch (Exception $e) {
        // Handle validation error
    }
}
```

## Usage in Application

### Storing Criteria
```php
$criteria = [
    ['name' => 'Age requirement', 'description' => 'Must be between 18-65 years'],
    ['name' => 'Education', 'description' => 'Bachelor degree required']
];

$exerciseModel->update($exerciseId, [
    'pre_screen_criteria' => json_encode($criteria)
]);
```

### Retrieving Criteria
```php
$exercise = $exerciseModel->find($exerciseId);
$criteria = [];
if (!empty($exercise['pre_screen_criteria'])) {
    $criteria = json_decode($exercise['pre_screen_criteria'], true) ?? [];
}
```

## Frontend JavaScript Structure
```javascript
// Criteria list structure in JavaScript
let criteriaList = [
    {
        name: "Criteria Name",
        description: "Criteria Description"
    }
];

// Adding new criteria
const newCriteria = {
    name: document.getElementById('criteriaName').value,
    description: document.getElementById('criteriaDescription').value || ''
};
criteriaList.push(newCriteria);
```

## Future Enhancement Considerations

### Potential Additional Fields
```json
{
  "name": "string",
  "description": "string",
  "priority": "high|medium|low",
  "type": "mandatory|preferred|disqualifying",
  "weight": 1-10,
  "validation_rules": {},
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Grouping Support
```json
{
  "groups": [
    {
      "group_name": "Document Requirements",
      "criteria": [
        {"name": "RS 3.2 signed", "description": "..."},
        {"name": "Academic certificates", "description": "..."}
      ]
    }
  ]
}
```

## Notes
- The current implementation uses a simple array structure for ease of use
- All criteria are treated equally (no priority or weighting)
- The structure is designed to be backward compatible
- Future enhancements should maintain compatibility with the current structure

## Related Files
- `app/Models/ExerciseModel.php`
- `app/Controllers/ExercisesController.php`
- `app/Views/exercises/exercises_pre_screen_criteria.php`
- `app/Controllers/ApplicationPreScreeningController.php`

---
*Generated on: 2025-08-11*
*DERS Version: 1.0*
*Database: exercises.pre_screen_criteria field*

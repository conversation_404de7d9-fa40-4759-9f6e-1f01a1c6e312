<?php

namespace App\Controllers;

use App\Models\ExerciseModel;

class ExerciseSettingsController extends BaseController
{
    protected $exerciseModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->exerciseModel = new ExerciseModel();
        $this->session = session();
    }

    /**
     * [GET] Display exercise settings page
     * URI: /exercise_settings/(:num)
     */
    public function index($exerciseId)
    {
        // Get organization ID from session
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url('exercises'));
        }

        // Get exercise data
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('exercises'));
        }

        // Verify exercise belongs to current organization
        if ($exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Access denied. Exercise does not belong to your organization.');
            return redirect()->to(base_url('exercises'));
        }

        $data = [
            'title' => 'Exercise Settings - ' . $exercise['exercise_name'],
            'menu' => 'exercises',
            'exercise' => $exercise
        ];

        return view('exercise_settings/exercise_settings_index', $data);
    }
}

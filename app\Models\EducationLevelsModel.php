<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * EducationLevelsModel
 *
 * Model for the education_levels table
 */
class EducationLevelsModel extends Model
{
    protected $table = 'education_levels';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    protected $allowedFields = [
        'name',
        'remarks',
        'priority',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|max_length[150]|is_unique[education_levels.name,id,{id}]',
        'priority' => 'permit_empty|integer|greater_than_equal_to[0]'
    ];

    protected $validationMessages = [
        'name' => [
            'required'    => 'Education level name is required',
            'max_length'  => 'Education level name cannot exceed 150 characters',
            'is_unique'   => 'Education level name already exists'
        ],
        'priority' => [
            'integer'                => 'Priority must be a valid number',
            'greater_than_equal_to'  => 'Priority must be 0 or greater'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get all active education levels
     *
     * @return array
     */
    public function getActiveEducationLevels()
    {
        return $this->orderBy('priority', 'ASC')
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get education level by name
     *
     * @param string $name
     * @return array|null
     */
    public function getEducationLevelByName($name)
    {
        return $this->where('name', $name)->first();
    }

    /**
     * Search education levels by name
     *
     * @param string $search
     * @return array
     */
    public function searchEducationLevels($search)
    {
        return $this->like('name', $search)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get education level statistics
     *
     * @return array
     */
    public function getEducationLevelStatistics()
    {
        $stats = [];

        // Total education levels
        $stats['total'] = $this->countAllResults(false);

        // Education levels with remarks
        $stats['with_remarks'] = $this->where('remarks IS NOT NULL')
                                      ->where('remarks !=', '')
                                      ->countAllResults(false);

        return $stats;
    }

    /**
     * Get education levels ordered by priority
     *
     * @return array
     */
    public function getEducationLevelsByPriority()
    {
        return $this->orderBy('priority', 'ASC')
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Update education level priority
     *
     * @param int $id
     * @param int $priority
     * @return bool
     */
    public function updatePriority($id, $priority)
    {
        return $this->update($id, ['priority' => $priority]);
    }
}
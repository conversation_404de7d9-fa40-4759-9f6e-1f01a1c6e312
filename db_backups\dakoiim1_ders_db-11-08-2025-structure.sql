-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Aug 11, 2025 at 05:29 PM
-- Server version: 10.5.25-MariaDB-cll-lve
-- PHP Version: 8.1.32

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `dakoiim1_ders_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `applicants`
--

CREATE TABLE `applicants` (
  `id` int(11) NOT NULL,
  `unique_id` varchar(100) NOT NULL,
  `email` varchar(150) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `gender` enum('male','female') DEFAULT NULL,
  `dobirth` date DEFAULT NULL,
  `place_of_origin` varchar(150) DEFAULT NULL,
  `id_photo_path` varchar(255) DEFAULT NULL,
  `contact_details` varchar(300) DEFAULT NULL,
  `location_address` varchar(255) DEFAULT NULL,
  `id_numbers` varchar(100) DEFAULT NULL,
  `current_employer` varchar(150) DEFAULT NULL,
  `current_position` varchar(150) DEFAULT NULL,
  `current_salary` decimal(10,2) DEFAULT NULL,
  `citizenship` varchar(50) DEFAULT NULL,
  `marital_status` enum('single','married','divorced','widowed') DEFAULT NULL,
  `date_of_marriage` date DEFAULT NULL,
  `spouse_employer` varchar(150) DEFAULT NULL,
  `is_public_servant` tinyint(2) NOT NULL,
  `public_service_file_number` varchar(20) NOT NULL,
  `employee_of_org_id` int(11) UNSIGNED DEFAULT NULL,
  `children` text DEFAULT NULL,
  `offence_convicted` text DEFAULT NULL,
  `referees` text DEFAULT NULL,
  `how_did_you_hear_about_us` varchar(255) DEFAULT NULL,
  `signature_path` varchar(255) DEFAULT NULL,
  `publications` text DEFAULT NULL,
  `awards` text DEFAULT NULL,
  `status` tinyint(2) DEFAULT NULL,
  `activation_token` varchar(100) NOT NULL,
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores job applicant information and details';

-- --------------------------------------------------------

--
-- Table structure for table `applicants_experiences`
--

CREATE TABLE `applicants_experiences` (
  `id` int(11) UNSIGNED NOT NULL,
  `applicant_id` int(11) UNSIGNED NOT NULL COMMENT 'References applicants.applicant_id',
  `employer` varchar(255) NOT NULL,
  `employer_contacts_address` text DEFAULT NULL,
  `position` varchar(255) NOT NULL,
  `date_from` date NOT NULL,
  `date_to` date DEFAULT NULL,
  `achievements` text DEFAULT NULL,
  `work_description` text DEFAULT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `applicant_education`
--

CREATE TABLE `applicant_education` (
  `id` int(11) UNSIGNED NOT NULL,
  `applicant_id` int(11) UNSIGNED NOT NULL COMMENT 'References applicants.applicant_id',
  `institution` varchar(255) NOT NULL,
  `course` varchar(255) NOT NULL,
  `date_from` date NOT NULL,
  `date_to` date DEFAULT NULL,
  `education_level` int(11) DEFAULT NULL COMMENT 'Refers to education levels (e.g., diploma, degree)',
  `units` text DEFAULT NULL COMMENT 'Units or credits completed',
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `applicant_files`
--

CREATE TABLE `applicant_files` (
  `id` int(11) UNSIGNED NOT NULL,
  `applicant_id` int(11) UNSIGNED NOT NULL COMMENT 'References applicants.applicant_id',
  `file_title` varchar(255) NOT NULL COMMENT 'Title or name of the file',
  `file_description` text DEFAULT NULL COMMENT 'Optional description of the file',
  `file_path` varchar(255) NOT NULL COMMENT 'File storage path or URL',
  `file_extracted_texts` text DEFAULT NULL COMMENT 'Extracted text from file contents, if any',
  `created_by` int(11) UNSIGNED DEFAULT NULL COMMENT 'User ID who created the record',
  `updated_by` int(11) UNSIGNED DEFAULT NULL COMMENT 'User ID who last updated the record',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `appx_application_details`
--

CREATE TABLE `appx_application_details` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `exercise_id` int(11) NOT NULL,
  `applicant_id` int(11) NOT NULL,
  `position_id` int(11) NOT NULL,
  `application_number` varchar(20) NOT NULL,
  `email_address` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `place_of_origin` varchar(255) DEFAULT NULL,
  `id_photo_path` varchar(255) DEFAULT NULL,
  `contact_details` varchar(500) DEFAULT NULL,
  `location_address` varchar(255) DEFAULT NULL,
  `id_numbers` varchar(255) DEFAULT NULL,
  `current_employer` varchar(255) DEFAULT NULL,
  `current_position` varchar(255) DEFAULT NULL,
  `current_salary` decimal(10,2) DEFAULT NULL,
  `citizenship` varchar(50) DEFAULT NULL,
  `marital_status` varchar(20) DEFAULT NULL,
  `date_of_marriage` date DEFAULT NULL,
  `spouse_employer` varchar(255) DEFAULT NULL,
  `is_public_servant` tinyint(1) NOT NULL DEFAULT 0,
  `public_service_file_number` varchar(20) DEFAULT NULL,
  `employee_of_org_id` int(11) UNSIGNED DEFAULT NULL,
  `children` text DEFAULT NULL,
  `offence_convicted` text DEFAULT NULL,
  `referees` text DEFAULT NULL,
  `how_did_you_hear_about_us` varchar(255) DEFAULT NULL,
  `signature_path` varchar(255) DEFAULT NULL,
  `publications` text DEFAULT NULL,
  `awards` text DEFAULT NULL,
  `is_received` tinyint(1) NOT NULL DEFAULT 0,
  `received_status` varchar(11) NOT NULL,
  `received_by` int(11) UNSIGNED NOT NULL,
  `received_at` datetime NOT NULL,
  `application_status` varchar(50) DEFAULT NULL,
  `remarks` text NOT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `pre_screened_at` datetime DEFAULT NULL,
  `pre_screened_by` int(11) UNSIGNED DEFAULT NULL,
  `pre_screened_status` varchar(50) DEFAULT NULL,
  `pre_screened_remarks` text DEFAULT NULL,
  `pre_screened_criteria_results` longtext DEFAULT NULL,
  `profile_status` varchar(20) NOT NULL,
  `profile_details` longtext NOT NULL,
  `profiled_by` int(11) UNSIGNED DEFAULT NULL,
  `profiled_at` datetime DEFAULT NULL,
  `rating_capability_max` int(5) DEFAULT NULL,
  `rating_remarks` text DEFAULT NULL,
  `rating_status` varchar(11) DEFAULT NULL,
  `rated_by` int(11) UNSIGNED DEFAULT NULL,
  `rated_at` datetime DEFAULT NULL,
  `shortlist_status` varchar(11) DEFAULT NULL,
  `shortlisted_by` int(11) UNSIGNED DEFAULT NULL,
  `shortlisted_at` datetime DEFAULT NULL,
  `interviewed` varchar(11) DEFAULT NULL,
  `interviewed_by` int(11) UNSIGNED DEFAULT NULL,
  `interviewed_at` datetime DEFAULT NULL,
  `interview_rated` varchar(11) DEFAULT NULL,
  `pre_selection_status` varchar(11) DEFAULT NULL,
  `pre_selection_by` int(11) UNSIGNED DEFAULT NULL,
  `preselection_at` datetime DEFAULT NULL,
  `selected_status` varchar(20) DEFAULT NULL,
  `selected_by` int(11) UNSIGNED DEFAULT NULL,
  `selected_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `appx_application_education`
--

CREATE TABLE `appx_application_education` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `applicant_id` int(11) NOT NULL,
  `applicant_education_id` int(11) NOT NULL COMMENT 'id from applicant_education table',
  `institution` varchar(255) NOT NULL,
  `course` varchar(255) NOT NULL,
  `date_from` date NOT NULL,
  `date_to` date DEFAULT NULL,
  `education_level` int(11) DEFAULT NULL,
  `units` text DEFAULT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `appx_application_experiences`
--

CREATE TABLE `appx_application_experiences` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `applicant_id` int(11) NOT NULL,
  `applicant_experience_id` int(11) NOT NULL COMMENT 'id from the applicant_experiences table',
  `employer` varchar(255) NOT NULL,
  `employer_contacts_address` text DEFAULT NULL,
  `position` varchar(255) NOT NULL,
  `date_from` date NOT NULL,
  `date_to` date DEFAULT NULL,
  `achievements` text DEFAULT NULL,
  `work_description` text DEFAULT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `appx_application_files`
--

CREATE TABLE `appx_application_files` (
  `id` int(11) UNSIGNED NOT NULL,
  `application_id` int(11) UNSIGNED NOT NULL COMMENT 'References appx_application_details.id',
  `applicant_id` int(11) UNSIGNED NOT NULL COMMENT 'References applicants.applicant_id',
  `applicant_file_id` int(11) NOT NULL COMMENT 'id from the applicantion_files table',
  `file_title` varchar(255) NOT NULL COMMENT 'Title of the uploaded file',
  `file_description` text DEFAULT NULL COMMENT 'Optional description of the file',
  `file_path` varchar(255) NOT NULL COMMENT 'Storage path to the uploaded file',
  `extracted_texts` longtext DEFAULT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `appx_application_profile`
--

CREATE TABLE `appx_application_profile` (
  `id` int(11) UNSIGNED NOT NULL,
  `application_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `sex` varchar(100) NOT NULL,
  `age` int(3) NOT NULL,
  `place_origin` varchar(255) NOT NULL,
  `contact_details` varchar(255) DEFAULT NULL,
  `nid_number` varchar(100) DEFAULT NULL,
  `current_employer` varchar(255) DEFAULT NULL,
  `current_position` varchar(255) DEFAULT NULL,
  `address_location` varchar(255) NOT NULL,
  `qualification_text` text NOT NULL,
  `other_trainings` text DEFAULT NULL,
  `knowledge` text NOT NULL,
  `skills_competencies` text NOT NULL,
  `job_experiences` text NOT NULL,
  `publications` text DEFAULT NULL,
  `awards` text DEFAULT NULL,
  `referees` text DEFAULT NULL,
  `comments` text DEFAULT NULL,
  `remarks` text NOT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `appx_application_rating`
--

CREATE TABLE `appx_application_rating` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `rate_item_id` int(11) NOT NULL,
  `score_grained` int(11) NOT NULL,
  `score_set` int(11) NOT NULL,
  `justification` text NOT NULL COMMENT 'justification of the score gained.',
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_llm_models`
--

CREATE TABLE `dakoii_llm_models` (
  `id` int(11) NOT NULL,
  `model_name` varchar(100) NOT NULL,
  `provider` varchar(50) NOT NULL,
  `api_key` varchar(255) NOT NULL,
  `base_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_org`
--

CREATE TABLE `dakoii_org` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_code` varchar(100) NOT NULL,
  `org_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `location_lock_province` varchar(100) DEFAULT NULL,
  `location_lock_country` varchar(100) DEFAULT NULL,
  `logo_path` varchar(255) DEFAULT NULL,
  `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
  `postal_address` text DEFAULT NULL,
  `phone_numbers` text DEFAULT NULL,
  `email_addresses` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `license_status` varchar(50) DEFAULT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `dakoii_users`
--

CREATE TABLE `dakoii_users` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `orgcode` varchar(500) NOT NULL,
  `role` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `education_levels`
--

CREATE TABLE `education_levels` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(150) NOT NULL COMMENT 'Education level name',
  `remarks` text DEFAULT NULL COMMENT 'Additional notes or remarks',
  `priority` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `created_by` int(11) UNSIGNED DEFAULT NULL COMMENT 'User ID who created the record',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) UNSIGNED DEFAULT NULL COMMENT 'User ID who last updated the record',
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `exercises`
--

CREATE TABLE `exercises` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `exercise_name` varchar(255) NOT NULL,
  `gazzetted_no` varchar(255) NOT NULL,
  `gazzetted_date` date NOT NULL,
  `advertisement_no` varchar(255) NOT NULL,
  `advertisement_date` date NOT NULL,
  `is_internal` tinyint(1) NOT NULL COMMENT 'is internal advertisment or external advertisement',
  `mode_of_advertisement` varchar(100) NOT NULL,
  `publish_date_from` datetime NOT NULL,
  `publish_date_to` datetime NOT NULL,
  `description` text DEFAULT NULL,
  `applicants_information` text DEFAULT NULL COMMENT 'Information provided to applicants',
  `applicants_notice` text DEFAULT NULL COMMENT 'Notice to applicants',
  `pre_screen_criteria` text NOT NULL,
  `status` enum('published','draft','selection','archived') NOT NULL DEFAULT 'draft',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores job advertisement exercises';

-- --------------------------------------------------------

--
-- Table structure for table `geo_countries`
--

CREATE TABLE `geo_countries` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `country_code` char(2) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_by` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `geo_districts`
--

CREATE TABLE `geo_districts` (
  `id` int(11) NOT NULL,
  `district_code` varchar(10) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `country_id` int(10) UNSIGNED NOT NULL,
  `province_id` int(10) UNSIGNED NOT NULL,
  `json_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_by` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `geo_provinces`
--

CREATE TABLE `geo_provinces` (
  `id` int(10) UNSIGNED NOT NULL,
  `province_code` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `country_id` int(10) UNSIGNED NOT NULL,
  `json_id` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `updated_by` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `positions`
--

CREATE TABLE `positions` (
  `id` int(11) UNSIGNED NOT NULL,
  `exercise_id` int(11) UNSIGNED DEFAULT NULL,
  `org_id` int(11) NOT NULL,
  `position_group_id` int(11) UNSIGNED DEFAULT NULL,
  `position_reference` varchar(50) NOT NULL,
  `designation` varchar(255) NOT NULL,
  `classification` varchar(100) NOT NULL,
  `award` varchar(100) NOT NULL,
  `location` varchar(255) NOT NULL,
  `annual_salary` varchar(100) NOT NULL,
  `qualifications` text NOT NULL,
  `knowledge` text NOT NULL,
  `skills_competencies` text NOT NULL,
  `job_experiences` text NOT NULL,
  `jd_filepath` varchar(255) NOT NULL,
  `jd_texts_extracted` text NOT NULL,
  `remarks` text NOT NULL,
  `status` enum('active','withdrawn') NOT NULL DEFAULT 'active',
  `status_at` datetime DEFAULT NULL,
  `status_by` int(11) UNSIGNED DEFAULT NULL,
  `status_remarks` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores available job positions';

-- --------------------------------------------------------

--
-- Table structure for table `positions_groups`
--

CREATE TABLE `positions_groups` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `exercise_id` int(11) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `group_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_by` varchar(255) NOT NULL,
  `updated_by` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `position_views`
--

CREATE TABLE `position_views` (
  `id` int(11) UNSIGNED NOT NULL,
  `position_id` int(11) UNSIGNED NOT NULL COMMENT 'References positions.id',
  `viewer_ip` varchar(45) DEFAULT NULL COMMENT 'IP address of the viewer',
  `gps_coordinates` varchar(50) DEFAULT NULL COMMENT 'GPS coordinates in format: latitude,longitude',
  `user_agent` text DEFAULT NULL COMMENT 'Browser user agent string',
  `session_id` varchar(128) DEFAULT NULL COMMENT 'Session ID to prevent duplicate counting',
  `viewed_at` datetime NOT NULL COMMENT 'When the position was viewed',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Tracks position views with geographical information';

-- --------------------------------------------------------

--
-- Table structure for table `rate_items`
--

CREATE TABLE `rate_items` (
  `id` int(11) UNSIGNED NOT NULL,
  `item_label` varchar(150) NOT NULL COMMENT 'Label or title of the item',
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rate_items_scores`
--

CREATE TABLE `rate_items_scores` (
  `id` int(11) UNSIGNED NOT NULL,
  `item_id` int(11) UNSIGNED NOT NULL COMMENT 'ID of the item in adx_items',
  `score` int(11) NOT NULL COMMENT 'Score value for the item',
  `label` varchar(100) NOT NULL COMMENT 'label that will appear of the item\r\n',
  `score_description` varchar(255) DEFAULT NULL COMMENT 'Description or justification for the score',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `orgcode` int(11) NOT NULL,
  `fileno` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','supervisor','user','guest') NOT NULL DEFAULT 'user',
  `position` varchar(255) DEFAULT NULL,
  `id_photo` varchar(500) NOT NULL,
  `phone` varchar(200) NOT NULL,
  `email` varchar(500) NOT NULL,
  `status` varchar(20) NOT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `applicants`
--
ALTER TABLE `applicants`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uq_email` (`email`),
  ADD UNIQUE KEY `uq_unique_id` (`unique_id`),
  ADD KEY `idx_applicants_email` (`email`),
  ADD KEY `idx_applicants_status` (`status`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `applicants_experiences`
--
ALTER TABLE `applicants_experiences`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_applicant_id` (`applicant_id`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_updated_by` (`updated_by`);

--
-- Indexes for table `applicant_education`
--
ALTER TABLE `applicant_education`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_applicant_id` (`applicant_id`),
  ADD KEY `idx_education_level` (`education_level`);

--
-- Indexes for table `applicant_files`
--
ALTER TABLE `applicant_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_applicant_id` (`applicant_id`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_updated_by` (`updated_by`);

--
-- Indexes for table `appx_application_details`
--
ALTER TABLE `appx_application_details`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_pre_screened` (`pre_screened_at`),
  ADD KEY `idx_pre_screened_status` (`pre_screened_status`);

--
-- Indexes for table `appx_application_education`
--
ALTER TABLE `appx_application_education`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `appx_application_experiences`
--
ALTER TABLE `appx_application_experiences`
  ADD PRIMARY KEY (`id`),
  ADD KEY `application_id_idx` (`application_id`),
  ADD KEY `applicant_id_idx` (`applicant_id`),
  ADD KEY `date_from_idx` (`date_from`);

--
-- Indexes for table `appx_application_files`
--
ALTER TABLE `appx_application_files`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `appx_application_profile`
--
ALTER TABLE `appx_application_profile`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by_index` (`created_by`),
  ADD KEY `updated_by_index` (`updated_by`);

--
-- Indexes for table `appx_application_rating`
--
ALTER TABLE `appx_application_rating`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_application_id` (`application_id`),
  ADD KEY `idx_rate_item_id` (`rate_item_id`),
  ADD KEY `idx_created_by` (`created_by`);

--
-- Indexes for table `dakoii_llm_models`
--
ALTER TABLE `dakoii_llm_models`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `education_levels`
--
ALTER TABLE `education_levels`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `exercises`
--
ALTER TABLE `exercises`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_exercises_status` (`status`);

--
-- Indexes for table `geo_countries`
--
ALTER TABLE `geo_countries`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uq_country_code` (`country_code`);

--
-- Indexes for table `geo_districts`
--
ALTER TABLE `geo_districts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_country_id` (`country_id`),
  ADD KEY `idx_province_id` (`province_id`);

--
-- Indexes for table `geo_provinces`
--
ALTER TABLE `geo_provinces`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uq_province_code` (`province_code`),
  ADD KEY `idx_country_id` (`country_id`);

--
-- Indexes for table `positions`
--
ALTER TABLE `positions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `positions_groups`
--
ALTER TABLE `positions_groups`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `position_views`
--
ALTER TABLE `position_views`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_position_id` (`position_id`),
  ADD KEY `idx_viewer_ip` (`viewer_ip`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_viewed_at` (`viewed_at`),
  ADD KEY `idx_position_session` (`position_id`,`session_id`),
  ADD KEY `idx_gps_coordinates` (`gps_coordinates`);

--
-- Indexes for table `rate_items`
--
ALTER TABLE `rate_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `rate_items_scores`
--
ALTER TABLE `rate_items_scores`
  ADD PRIMARY KEY (`id`),
  ADD KEY `item_id` (`item_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `applicants`
--
ALTER TABLE `applicants`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `applicants_experiences`
--
ALTER TABLE `applicants_experiences`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `applicant_education`
--
ALTER TABLE `applicant_education`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `applicant_files`
--
ALTER TABLE `applicant_files`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `appx_application_details`
--
ALTER TABLE `appx_application_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `appx_application_education`
--
ALTER TABLE `appx_application_education`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `appx_application_experiences`
--
ALTER TABLE `appx_application_experiences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `appx_application_files`
--
ALTER TABLE `appx_application_files`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `appx_application_profile`
--
ALTER TABLE `appx_application_profile`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `appx_application_rating`
--
ALTER TABLE `appx_application_rating`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `dakoii_llm_models`
--
ALTER TABLE `dakoii_llm_models`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `dakoii_org`
--
ALTER TABLE `dakoii_org`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `dakoii_users`
--
ALTER TABLE `dakoii_users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `education_levels`
--
ALTER TABLE `education_levels`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `exercises`
--
ALTER TABLE `exercises`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `geo_countries`
--
ALTER TABLE `geo_countries`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `geo_districts`
--
ALTER TABLE `geo_districts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `geo_provinces`
--
ALTER TABLE `geo_provinces`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `positions`
--
ALTER TABLE `positions`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `positions_groups`
--
ALTER TABLE `positions_groups`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `position_views`
--
ALTER TABLE `position_views`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `rate_items`
--
ALTER TABLE `rate_items`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `rate_items_scores`
--
ALTER TABLE `rate_items_scores`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `position_views`
--
ALTER TABLE `position_views`
  ADD CONSTRAINT `fk_position_views_position_id` FOREIGN KEY (`position_id`) REFERENCES `positions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

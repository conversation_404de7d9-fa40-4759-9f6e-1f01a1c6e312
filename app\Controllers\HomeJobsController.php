<?php

namespace App\Controllers;

use App\Services\ViewTrackingService;

class HomeJobsController extends BaseController
{
    protected $session;
    protected $viewTrackingService;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->viewTrackingService = new ViewTrackingService();
    }

    /**
     * Display all published job positions
     */
    public function index()
    {
        try {
            // Get filter parameters
            $selectedOrgId = $this->request->getGet('org_id');
            $searchTerm = $this->request->getGet('search');
            $selectedClassification = $this->request->getGet('classification');
            $selectedPositionGroup = $this->request->getGet('position_group');
            $selectedPositionType = $this->request->getGet('position_type');

            // Get pagination parameters for lazy loading
            $page = (int)($this->request->getGet('page') ?? 1);
            $isAjax = $this->request->getGet('ajax') === '1';

            // Set limits: 10 for initial load, 50 for subsequent loads
            $limit = ($page === 1) ? 10 : 50;
            $offset = ($page === 1) ? 0 : 10 + (($page - 2) * 50);

            // Load models
            $positionsModel = new \App\Models\PositionsModel();
            $orgModel = new \App\Models\DakoiiOrgModel();

            // Build the query for positions from published exercises
            $query = $positionsModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.is_internal
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.status', 'active')
            ->where('exercises.status', 'published');

            // Apply filters
            if ($selectedOrgId) {
                $query->where('positions.org_id', $selectedOrgId);
            }

            if ($searchTerm) {
                $query->groupStart()
                     ->like('positions.designation', $searchTerm)
                     ->orLike('positions.position_reference', $searchTerm)
                     ->orLike('dakoii_org.org_name', $searchTerm)
                     ->orLike('exercises.exercise_name', $searchTerm)
                     ->orLike('positions_groups.group_name', $searchTerm)
                     ->groupEnd();
            }

            if ($selectedClassification) {
                $query->where('positions.classification', $selectedClassification);
            }

            if ($selectedPositionGroup) {
                $query->where('positions.position_group_id', $selectedPositionGroup);
            }

            if ($selectedPositionType) {
                if ($selectedPositionType === 'internal') {
                    $query->where('exercises.is_internal', 1);
                } elseif ($selectedPositionType === 'external') {
                    $query->where('exercises.is_internal', 0);
                }
            }

            // Get positions with limit and offset for lazy loading
            $positions = $query->orderBy('RAND()') // Random order every time
                              ->limit($limit, $offset)
                              ->findAll();

            // Get all active organizations for filter dropdown
            $organizations = $orgModel->where('is_active', 1)
                                    ->orderBy('org_name', 'ASC')
                                    ->findAll();

            // Extract unique classifications for filtering
            $allPositions = $positionsModel->select('classification')
                                          ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                                          ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                                          ->where('positions.status', 'active')
                                          ->where('exercises.status', 'published')
                                          ->findAll();

            $classifications = array_unique(array_filter(array_column($allPositions, 'classification')));

            // Get position groups for filtering
            $positionGroupsModel = new \App\Models\PositionsGroupModel();
            $positionGroups = $positionGroupsModel->select('positions_groups.id, positions_groups.group_name')
                                                 ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                                                 ->where('exercises.status', 'published')
                                                 ->orderBy('positions_groups.group_name', 'ASC')
                                                 ->findAll();

            // Handle AJAX requests for lazy loading
            if ($isAjax) {
                return $this->handleAjaxRequest($positions);
            }

            // Return the view with data for initial page load
            return view('home/home_jobs', [
                'title' => 'Available Positions',
                'menu' => 'jobs',
                'positions' => $positions,
                'organizations' => $organizations,
                'classifications' => $classifications,
                'positionGroups' => $positionGroups,
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'selectedClassification' => $selectedClassification,
                'selectedPositionGroup' => $selectedPositionGroup,
                'selectedPositionType' => $selectedPositionType
            ]);
        } catch (\Exception $e) {
            // Log the error and show empty positions
            log_message('error', 'Error loading jobs page: ' . $e->getMessage());

            return view('home/home_jobs', [
                'title' => 'Available Positions',
                'menu' => 'jobs',
                'positions' => [],
                'organizations' => [],
                'classifications' => [],
                'locations' => [],
                'pager' => null,
                'selectedOrgId' => null,
                'searchTerm' => null,
                'selectedClassification' => null,
                'selectedLocation' => null,
                'selectedPositionType' => null,
                'error' => 'Unable to load positions. Please try again later.'
            ]);
        }
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/jobs');
        }

        try {
            // Load models
            $positionsModel = new \App\Models\PositionsModel();

            // First try to get the position with minimal joins to see if it exists
            $position = $positionsModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.id', $id)
            ->where('positions.status', 'active')
            ->first();

            if ($position === null) {
                return redirect()->to('/jobs')->with('error', 'Position not found or not available.');
            }

            // Try to get additional exercise information if available
            $exerciseInfo = $positionsModel->select('
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.status as exercise_status,
                exercises.is_internal,
                exercises.applicants_information as applicant_information,
                exercises.pre_screen_criteria as applicant_notice
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.id', $id)
            ->first();

            // Merge exercise information if available
            if ($exerciseInfo) {
                $position = array_merge($position, $exerciseInfo);
            }

            // Track position view
            $this->viewTrackingService->trackView($id);

            return view('home/home_job_details', [
                'title' => $position['designation'],
                'menu' => 'jobs',
                'position' => $position
            ]);

        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Error loading position details: ' . $e->getMessage());

            return redirect()->to('/jobs')->with('error', 'Unable to load position details. Please try again later.');
        }
    }

    /**
     * Handle AJAX requests for lazy loading
     */
    private function handleAjaxRequest($positions)
    {
        try {
            if (empty($positions)) {
                return $this->response->setJSON([
                    'success' => true,
                    'positions' => [],
                    'gridHtml' => '',
                    'tableHtml' => ''
                ]);
            }

            // Generate grid HTML
            $gridHtml = '';
            foreach ($positions as $position) {
                $gridHtml .= $this->generateGridItemHtml($position);
            }

            // Generate table HTML
            $tableHtml = '';
            foreach ($positions as $position) {
                $tableHtml .= $this->generateTableRowHtml($position);
            }

            return $this->response->setJSON([
                'success' => true,
                'positions' => $positions,
                'gridHtml' => $gridHtml,
                'tableHtml' => $tableHtml
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error in AJAX request: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'error' => 'Unable to load more positions'
            ]);
        }
    }

    /**
     * Generate HTML for grid item
     */
    private function generateGridItemHtml($position)
    {
        $html = '<div class="col-lg-4 col-md-6">
                    <div class="card job-card h-100">
                      <div class="card-body p-4">
                        <h5 class="position-title mb-3">
                          <a href="' . base_url('jobs/view/' . ($position['id'] ?? '1')) . '" class="text-decoration-none">
                            ' . esc($position['designation'] ?? 'Position Not Specified') . '
                          </a>
                        </h5>
                        <div class="position-meta mb-3">
                          <div class="mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-building me-2" viewBox="0 0 16 16">
                              <path d="M4 2.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm3 0a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm3.5-.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1ZM4 5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM7.5 5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Zm2.5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM4.5 8a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Zm2.5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm3.5-.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Z"/>
                              <path d="M2 1a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V1Zm11 0H3v14h3v-2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5V15h3V1Z"/>
                            </svg>
                            ' . esc($position['org_name'] ?? 'Organization Not Specified') . '
                          </div>
                          <div class="mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-collection me-2" viewBox="0 0 16 16">
                              <path d="M2.5 3.5a.5.5 0 0 1 0-1h11a.5.5 0 0 1 0 1h-11zm2-2a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1h-7zM0 13a1.5 1.5 0 0 0 1.5 1.5h13A1.5 1.5 0 0 0 16 13V6a1.5 1.5 0 0 0-1.5-1.5h-13A1.5 1.5 0 0 0 0 6v7zm1.5.5A.5.5 0 0 1 1 13V6a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-13z"/>
                            </svg>
                            ' . esc($position['group_name'] ?? 'Position Group Not Specified') . '
                          </div>
                          <div class="mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt me-2" viewBox="0 0 16 16">
                              <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                              <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                            </svg>
                            ' . esc($position['location'] ?? 'Location Not Specified') . '
                          </div>
                          <div class="mb-2">
                            <span class="badge bg-yellow me-2">' . esc($position['classification'] ?? 'Not Specified') . '</span>';

        if (isset($position['is_internal'])) {
            if ($position['is_internal'] == 1) {
                $html .= '<span class="badge bg-warning text-dark">Internal</span>';
            } else {
                $html .= '<span class="badge bg-primary">External</span>';
            }
        }

        $html .= '      </div>
                          <div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cash me-2" viewBox="0 0 16 16">
                              <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                              <path d="M0 4a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V6a2 2 0 0 1-2-2H3z"/>
                            </svg>
                            K' . esc($position['annual_salary'] ?? '0') . '
                          </div>
                        </div>
                        <div class="position-deadline mb-3">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar-event me-2" viewBox="0 0 16 16">
                            <path d="M11 6.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                            <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                          </svg>
                          Closes on ' . (isset($position['publish_date_to']) ? date('d M Y \a\t g:i A', strtotime($position['publish_date_to'])) : 'Date Not Specified') . '
                        </div>
                        <a href="' . base_url('jobs/view/' . ($position['id'] ?? '1')) . '" class="btn btn-red w-100">View Details</a>
                      </div>
                    </div>
                  </div>';

        return $html;
    }

    /**
     * Generate HTML for table row
     */
    private function generateTableRowHtml($position)
    {
        $html = '<tr>
                    <td>
                      <div class="fw-bold text-red">' . esc($position['designation'] ?? 'Position Not Specified') . '</div>
                      <small class="text-muted">' . esc($position['advertisement_no'] ?? 'N/A') . '</small>
                    </td>
                    <td>' . esc($position['org_name'] ?? 'Organization Not Specified') . '</td>
                    <td>' . esc($position['group_name'] ?? 'Position Group Not Specified') . '</td>
                    <td>' . esc($position['location'] ?? 'Location Not Specified') . '</td>
                    <td><span class="badge bg-yellow">' . esc($position['classification'] ?? 'Not Specified') . '</span></td>
                    <td>';

        if (isset($position['is_internal'])) {
            if ($position['is_internal'] == 1) {
                $html .= '<span class="badge bg-warning text-dark">Internal</span>';
            } else {
                $html .= '<span class="badge bg-primary">External</span>';
            }
        } else {
            $html .= '<span class="badge bg-secondary">N/A</span>';
        }

        $html .= '    </td>
                    <td>K' . esc($position['annual_salary'] ?? '0') . '</td>
                    <td>' . (isset($position['publish_date_to']) ? date('d M Y \a\t g:i A', strtotime($position['publish_date_to'])) : 'Date Not Specified') . '</td>
                    <td>
                      <a href="' . base_url('jobs/view/' . ($position['id'] ?? '1')) . '" class="btn btn-sm btn-red">View Details</a>
                    </td>
                  </tr>';

        return $html;
    }
}

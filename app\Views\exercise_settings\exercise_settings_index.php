<?php
/**
 * Exercise Settings Index View
 * Displays settings options for managing exercise configurations
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-cog me-2"></i>Exercise Settings</h2>
            <p class="text-muted">Manage settings and configurations for this exercise</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('exercises') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
    </div>

    <!-- Exercise Information Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Exercise Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Exercise Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                            <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no'] ?? 'N/A') ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <span class="badge <?= $exercise['status'] === 'draft' ? 'bg-secondary' : ($exercise['status'] === 'publish' ? 'bg-success' : ($exercise['status'] === 'selection' ? 'bg-primary' : 'bg-danger')) ?>">
                                    <?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?>
                                </span>
                            </p>
                            <p><strong>Created:</strong> <?= date('M d, Y', strtotime($exercise['created_at'])) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Options -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>Settings & Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Pre-Screening Criteria -->
                        <div class="col-md-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-filter fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">Pre-Screening Criteria</h5>
                                    <p class="card-text text-muted">Define and manage pre-screening criteria for job exercises</p>
                                    <a href="<?= base_url('exercises/pre_screen_criteria/' . $exercise['id']) ?>" class="btn btn-warning">
                                        <i class="fas fa-filter me-1"></i> Manage Criteria
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Applicant Information -->
                        <div class="col-md-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-info-circle fa-3x text-info"></i>
                                    </div>
                                    <h5 class="card-title">Applicant Information</h5>
                                    <p class="card-text text-muted">Manage information requirements for applicants</p>
                                    <a href="<?= base_url('exercises/applicant_information/' . $exercise['id']) ?>" class="btn btn-info">
                                        <i class="fas fa-info-circle me-1"></i> Manage Information
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Applicant Notice -->
                        <div class="col-md-4">
                            <div class="card h-100 border-dark">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-bell fa-3x text-dark"></i>
                                    </div>
                                    <h5 class="card-title">Applicant Notice</h5>
                                    <p class="card-text text-muted">Create and manage notices for applicants</p>
                                    <a href="<?= base_url('exercises/applicant_notice/' . $exercise['id']) ?>" class="btn btn-dark">
                                        <i class="fas fa-bell me-1"></i> Manage Notice
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Pre-Screening Position -->
                        <div class="col-md-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-list-check fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">Pre-Screening Position</h5>
                                    <p class="card-text text-muted">Select which positions must go through pre-screening</p>
                                    <a href="<?= base_url('exercise_prescreening_positions/' . $exercise['id']) ?>" class="btn btn-success">
                                        <i class="fas fa-list-check me-1"></i> Manage Positions
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add any JavaScript functionality if needed
    console.log('Exercise Settings page loaded');
});
</script>
<?= $this->endSection() ?>

<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="text-light-text"><?= esc($org['name']) ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/exercise/view/' . $exercise['id']) ?>" class="text-light-text"><?= esc($exercise['exercise_name']) ?></a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Edit Exercise</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-light-text mb-1">Edit Exercise</h2>
            <p class="text-secondary mb-0">Update exercise information for <?= esc($org['name']) ?></p>
        </div>
        <div class="d-flex gap-2">
            <a href="<?= base_url('dakoii/organization/exercise/view/' . $exercise['id']) ?>" class="btn btn-outline-secondary text-light-text">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercise
            </a>
            <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-info text-light-text">
                <i class="fas fa-building me-1"></i> Back to Organization
            </a>
        </div>
    </div>

    <!-- Exercise Edit Form -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-warning py-3">
                    <h5 class="fw-bold mb-0 text-dark"><i class="fas fa-edit me-2"></i>Edit Exercise Information</h5>
                </div>
                <div class="card-body p-4">
                    <form id="exerciseEditForm" method="post">
                        <?= csrf_field() ?>
                        <input type="hidden" name="id" value="<?= $exercise['id'] ?>">
                        <input type="hidden" name="org_id" value="<?= $org['id'] ?>">
                        <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">

                        <div class="row">
                            <!-- Exercise Name -->
                            <div class="col-md-6 mb-3">
                                <label for="exercise_name" class="form-label text-light-text">Exercise Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="exercise_name" name="exercise_name" value="<?= esc($exercise['exercise_name']) ?>" required>
                                <div class="form-text text-secondary">Enter the name of the recruitment exercise</div>
                            </div>

                            <!-- Gazzetted Number -->
                            <div class="col-md-6 mb-3">
                                <label for="gazzetted_no" class="form-label text-light-text">Gazzetted Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="gazzetted_no" name="gazzetted_no" value="<?= esc($exercise['gazzetted_no']) ?>" required>
                                <div class="form-text text-secondary">Official gazette reference number</div>
                            </div>

                            <!-- Gazzetted Date -->
                            <div class="col-md-6 mb-3">
                                <label for="gazzetted_date" class="form-label text-light-text">Gazzetted Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="gazzetted_date" name="gazzetted_date" value="<?= $exercise['gazzetted_date'] ?>" required>
                            </div>

                            <!-- Advertisement Number -->
                            <div class="col-md-6 mb-3">
                                <label for="advertisement_no" class="form-label text-light-text">Advertisement Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="advertisement_no" name="advertisement_no" value="<?= esc($exercise['advertisement_no']) ?>" required>
                                <div class="form-text text-secondary">Advertisement reference number</div>
                            </div>

                            <!-- Advertisement Date -->
                            <div class="col-md-6 mb-3">
                                <label for="advertisement_date" class="form-label text-light-text">Advertisement Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="advertisement_date" name="advertisement_date" value="<?= $exercise['advertisement_date'] ?>" required>
                            </div>

                            <!-- Mode of Advertisement -->
                            <div class="col-md-6 mb-3">
                                <label for="mode_of_advertisement" class="form-label text-light-text">Mode of Advertisement <span class="text-danger">*</span></label>
                                <select class="form-select" id="mode_of_advertisement" name="mode_of_advertisement" required>
                                    <option value="">Select Mode</option>
                                    <option value="Online" <?= $exercise['mode_of_advertisement'] == 'Online' ? 'selected' : '' ?>>Online</option>
                                    <option value="Print" <?= $exercise['mode_of_advertisement'] == 'Print' ? 'selected' : '' ?>>Print Media</option>
                                    <option value="Print & Online" <?= $exercise['mode_of_advertisement'] == 'Print & Online' ? 'selected' : '' ?>>Print & Online</option>
                                    <option value="Radio" <?= $exercise['mode_of_advertisement'] == 'Radio' ? 'selected' : '' ?>>Radio</option>
                                    <option value="Television" <?= $exercise['mode_of_advertisement'] == 'Television' ? 'selected' : '' ?>>Television</option>
                                    <option value="Multiple Media" <?= $exercise['mode_of_advertisement'] == 'Multiple Media' ? 'selected' : '' ?>>Multiple Media</option>
                                </select>
                            </div>

                            <!-- Publish Date From -->
                            <div class="col-md-6 mb-3">
                                <label for="publish_date_from" class="form-label text-light-text">Publish Date From <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="publish_date_from" name="publish_date_from" value="<?= date('Y-m-d\TH:i', strtotime($exercise['publish_date_from'])) ?>" required>
                            </div>

                            <!-- Publish Date To -->
                            <div class="col-md-6 mb-3">
                                <label for="publish_date_to" class="form-label text-light-text">Publish Date To <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="publish_date_to" name="publish_date_to" value="<?= date('Y-m-d\TH:i', strtotime($exercise['publish_date_to'])) ?>" required>
                            </div>

                            <!-- Description -->
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label text-light-text">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4" placeholder="Enter exercise description (optional)"><?= esc($exercise['description'] ?? '') ?></textarea>
                                <div class="form-text text-secondary">Provide additional details about the exercise</div>
                            </div>
                        </div>

                        <!-- Current Status Display -->
                        <div class="alert alert-info">
                            <h6 class="alert-heading"><i class="fas fa-info-circle me-1"></i> Current Status</h6>
                            <p class="mb-0">
                                This exercise is currently in 
                                <strong><?= ucfirst(str_replace('_', ' ', $exercise['status'])) ?></strong> status.
                                You can change the status after saving the exercise information.
                            </p>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="<?= base_url('dakoii/organization/exercise/view/' . $exercise['id']) ?>" class="btn btn-outline-secondary text-light-text">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning text-dark">
                                <i class="fas fa-save me-1"></i> Update Exercise
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('exerciseEditForm');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(form);
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Updating...';
        submitBtn.disabled = true;
        
        // Submit to ExercisesController API
        fetch('<?= base_url('exercises/update/' . $exercise['id']) ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message and redirect
                alert('Exercise updated successfully!');
                window.location.href = '<?= base_url('dakoii/organization/exercise/view/' . $exercise['id']) ?>';
            } else {
                alert('Error: ' + (data.message || 'Failed to update exercise'));
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the exercise');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>

<?= $this->endSection() ?>

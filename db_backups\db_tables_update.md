Showing create queries
Tables
Table	Create table
applicants	CREATE TABLE `applicants` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `unique_id` varchar(100) NOT NULL,
 `email` varchar(150) NOT NULL,
 `password` varchar(255) NOT NULL,
 `first_name` varchar(100) NOT NULL,
 `last_name` varchar(100) NOT NULL,
 `gender` enum('male','female') DEFAULT NULL,
 `dobirth` date DEFAULT NULL,
 `place_of_origin` varchar(150) DEFAULT NULL,
 `id_photo_path` varchar(255) DEFAULT NULL,
 `contact_details` varchar(300) DEFAULT NULL,
 `location_address` varchar(255) DEFAULT NULL,
 `id_numbers` varchar(100) DEFAULT NULL,
 `current_employer` varchar(150) DEFAULT NULL,
 `current_position` varchar(150) DEFAULT NULL,
 `current_salary` decimal(10,2) DEFAULT NULL,
 `citizenship` varchar(50) DEFAULT NULL,
 `marital_status` enum('single','married','divorced','widowed') DEFAULT NULL,
 `date_of_marriage` date DEFAULT NULL,
 `spouse_employer` varchar(150) DEFAULT NULL,
 `is_public_servant` tinyint(2) NOT NULL,
 `public_service_file_number` varchar(20) NOT NULL,
 `employee_of_org_id` int(11) unsigned DEFAULT NULL,
 `children` text DEFAULT NULL,
 `offence_convicted` text DEFAULT NULL,
 `referees` text DEFAULT NULL,
 `how_did_you_hear_about_us` varchar(255) DEFAULT NULL,
 `signature_path` varchar(255) DEFAULT NULL,
 `publications` text DEFAULT NULL,
 `awards` text DEFAULT NULL,
 `status` tinyint(2) DEFAULT NULL,
 `activation_token` varchar(100) NOT NULL,
 `created_by` int(10) unsigned DEFAULT NULL,
 `updated_by` int(10) unsigned DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uq_email` (`email`),
 UNIQUE KEY `uq_unique_id` (`unique_id`),
 KEY `idx_applicants_email` (`email`),
 KEY `idx_applicants_status` (`status`),
 KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores job applicant information and details'
applicants_experiences	CREATE TABLE `applicants_experiences` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `applicant_id` int(11) unsigned NOT NULL COMMENT 'References applicants.applicant_id',
 `employer` varchar(255) NOT NULL,
 `employer_contacts_address` text DEFAULT NULL,
 `position` varchar(255) NOT NULL,
 `date_from` date NOT NULL,
 `date_to` date DEFAULT NULL,
 `achievements` text DEFAULT NULL,
 `work_description` text DEFAULT NULL,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_applicant_id` (`applicant_id`),
 KEY `idx_created_by` (`created_by`),
 KEY `idx_updated_by` (`updated_by`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
applicant_education	CREATE TABLE `applicant_education` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `applicant_id` int(11) unsigned NOT NULL COMMENT 'References applicants.applicant_id',
 `institution` varchar(255) NOT NULL,
 `course` varchar(255) NOT NULL,
 `date_from` date NOT NULL,
 `date_to` date DEFAULT NULL,
 `education_level` int(11) DEFAULT NULL COMMENT 'Refers to education levels (e.g., diploma, degree)',
 `units` text DEFAULT NULL COMMENT 'Units or credits completed',
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_applicant_id` (`applicant_id`),
 KEY `idx_education_level` (`education_level`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
applicant_files	CREATE TABLE `applicant_files` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `applicant_id` int(11) unsigned NOT NULL COMMENT 'References applicants.applicant_id',
 `file_title` varchar(255) NOT NULL COMMENT 'Title or name of the file',
 `file_description` text DEFAULT NULL COMMENT 'Optional description of the file',
 `file_path` varchar(255) NOT NULL COMMENT 'File storage path or URL',
 `file_extracted_texts` text DEFAULT NULL COMMENT 'Extracted text from file contents, if any',
 `created_by` int(11) unsigned DEFAULT NULL COMMENT 'User ID who created the record',
 `updated_by` int(11) unsigned DEFAULT NULL COMMENT 'User ID who last updated the record',
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_applicant_id` (`applicant_id`),
 KEY `idx_created_by` (`created_by`),
 KEY `idx_updated_by` (`updated_by`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
appx_application_details	CREATE TABLE `appx_application_details` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `exercise_id` int(11) NOT NULL,
 `applicant_id` int(11) NOT NULL,
 `position_id` int(11) NOT NULL,
 `application_number` varchar(20) NOT NULL,
 `email_address` varchar(255) NOT NULL,
 `first_name` varchar(100) NOT NULL,
 `last_name` varchar(100) NOT NULL,
 `gender` varchar(10) DEFAULT NULL,
 `date_of_birth` date DEFAULT NULL,
 `place_of_origin` varchar(255) DEFAULT NULL,
 `id_photo_path` varchar(255) DEFAULT NULL,
 `contact_details` varchar(500) DEFAULT NULL,
 `location_address` varchar(255) DEFAULT NULL,
 `id_numbers` varchar(255) DEFAULT NULL,
 `current_employer` varchar(255) DEFAULT NULL,
 `current_position` varchar(255) DEFAULT NULL,
 `current_salary` decimal(10,2) DEFAULT NULL,
 `citizenship` varchar(50) DEFAULT NULL,
 `marital_status` varchar(20) DEFAULT NULL,
 `date_of_marriage` date DEFAULT NULL,
 `spouse_employer` varchar(255) DEFAULT NULL,
 `is_public_servant` tinyint(1) NOT NULL DEFAULT 0,
 `public_service_file_number` varchar(20) DEFAULT NULL,
 `employee_of_org_id` int(11) unsigned DEFAULT NULL,
 `children` text DEFAULT NULL,
 `offence_convicted` text DEFAULT NULL,
 `referees` text DEFAULT NULL,
 `how_did_you_hear_about_us` varchar(255) DEFAULT NULL,
 `signature_path` varchar(255) DEFAULT NULL,
 `publications` text DEFAULT NULL,
 `awards` text DEFAULT NULL,
 `is_received` tinyint(1) NOT NULL DEFAULT 0,
 `received_status` varchar(11) NOT NULL,
 `received_by` int(11) unsigned NOT NULL,
 `received_at` datetime NOT NULL,
 `application_status` varchar(50) DEFAULT NULL,
 `remarks` text NOT NULL,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `pre_screened_at` datetime DEFAULT NULL,
 `pre_screened_by` int(11) unsigned DEFAULT NULL,
 `pre_screened_status` varchar(50) DEFAULT NULL,
 `pre_screened_remarks` text DEFAULT NULL,
 `pre_screened_criteria_results` longtext DEFAULT NULL,
 `pre_screened_ai_analysis` longtext NOT NULL,
 `profile_status` varchar(20) NOT NULL,
 `profile_details` longtext NOT NULL,
 `profiled_by` int(11) unsigned DEFAULT NULL,
 `profiled_at` datetime DEFAULT NULL,
 `rating_capability_max` int(5) DEFAULT NULL,
 `rating_remarks` text DEFAULT NULL,
 `rating_status` varchar(11) DEFAULT NULL,
 `rated_by` int(11) unsigned DEFAULT NULL,
 `rated_at` datetime DEFAULT NULL,
 `shortlist_status` varchar(11) DEFAULT NULL,
 `shortlisted_by` int(11) unsigned DEFAULT NULL,
 `shortlisted_at` datetime DEFAULT NULL,
 `interviewed` varchar(11) DEFAULT NULL,
 `interviewed_by` int(11) unsigned DEFAULT NULL,
 `interviewed_at` datetime DEFAULT NULL,
 `interview_rated` varchar(11) DEFAULT NULL,
 `pre_selection_status` varchar(11) DEFAULT NULL,
 `pre_selection_by` int(11) unsigned DEFAULT NULL,
 `preselection_at` datetime DEFAULT NULL,
 `selected_status` varchar(20) DEFAULT NULL,
 `selected_by` int(11) unsigned DEFAULT NULL,
 `selected_at` datetime DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_pre_screened` (`pre_screened_at`),
 KEY `idx_pre_screened_status` (`pre_screened_status`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
appx_application_education	CREATE TABLE `appx_application_education` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `application_id` int(11) NOT NULL,
 `applicant_id` int(11) NOT NULL,
 `applicant_education_id` int(11) NOT NULL COMMENT 'id from applicant_education table',
 `institution` varchar(255) NOT NULL,
 `course` varchar(255) NOT NULL,
 `date_from` date NOT NULL,
 `date_to` date DEFAULT NULL,
 `education_level` int(11) DEFAULT NULL,
 `units` text DEFAULT NULL,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
appx_application_experiences	CREATE TABLE `appx_application_experiences` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `application_id` int(11) NOT NULL,
 `applicant_id` int(11) NOT NULL,
 `applicant_experience_id` int(11) NOT NULL COMMENT 'id from the applicant_experiences table',
 `employer` varchar(255) NOT NULL,
 `employer_contacts_address` text DEFAULT NULL,
 `position` varchar(255) NOT NULL,
 `date_from` date NOT NULL,
 `date_to` date DEFAULT NULL,
 `achievements` text DEFAULT NULL,
 `work_description` text DEFAULT NULL,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `application_id_idx` (`application_id`),
 KEY `applicant_id_idx` (`applicant_id`),
 KEY `date_from_idx` (`date_from`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
appx_application_files	CREATE TABLE `appx_application_files` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `application_id` int(11) unsigned NOT NULL COMMENT 'References appx_application_details.id',
 `applicant_id` int(11) unsigned NOT NULL COMMENT 'References applicants.applicant_id',
 `applicant_file_id` int(11) NOT NULL COMMENT 'id from the applicantion_files table',
 `file_title` varchar(255) NOT NULL COMMENT 'Title of the uploaded file',
 `file_description` text DEFAULT NULL COMMENT 'Optional description of the file',
 `file_path` varchar(255) NOT NULL COMMENT 'Storage path to the uploaded file',
 `extracted_texts` longtext DEFAULT NULL,
 `ai_analysis_results` longtext DEFAULT NULL COMMENT 'AI analysis results in JSON format',
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
appx_application_profile	CREATE TABLE `appx_application_profile` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `application_id` int(11) NOT NULL,
 `name` varchar(255) NOT NULL,
 `sex` varchar(100) NOT NULL,
 `age` int(3) NOT NULL,
 `place_origin` varchar(255) NOT NULL,
 `contact_details` varchar(255) DEFAULT NULL,
 `nid_number` varchar(100) DEFAULT NULL,
 `current_employer` varchar(255) DEFAULT NULL,
 `current_position` varchar(255) DEFAULT NULL,
 `address_location` varchar(255) NOT NULL,
 `qualification_text` text NOT NULL,
 `other_trainings` text DEFAULT NULL,
 `knowledge` text NOT NULL,
 `skills_competencies` text NOT NULL,
 `job_experiences` text NOT NULL,
 `publications` text DEFAULT NULL,
 `awards` text DEFAULT NULL,
 `referees` text DEFAULT NULL,
 `comments` text DEFAULT NULL,
 `remarks` text NOT NULL,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `created_by_index` (`created_by`),
 KEY `updated_by_index` (`updated_by`)
) ENGINE=InnoDB AUTO_INCREMENT=157 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
appx_application_rating	CREATE TABLE `appx_application_rating` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `application_id` int(11) NOT NULL,
 `rate_item_id` int(11) NOT NULL,
 `score_grained` int(11) NOT NULL,
 `score_set` int(11) NOT NULL,
 `justification` text NOT NULL COMMENT 'justification of the score gained.',
 `created_by` int(11) unsigned DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_by` int(11) unsigned DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_application_id` (`application_id`),
 KEY `idx_rate_item_id` (`rate_item_id`),
 KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_llm_models	CREATE TABLE `dakoii_llm_models` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `model_name` varchar(100) NOT NULL,
 `provider` varchar(50) NOT NULL,
 `api_key` varchar(255) NOT NULL,
 `base_url` varchar(255) DEFAULT NULL,
 `is_active` tinyint(1) DEFAULT 1,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_org	CREATE TABLE `dakoii_org` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_code` varchar(100) NOT NULL,
 `org_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `location_lock_province` varchar(100) DEFAULT NULL,
 `location_lock_country` varchar(100) DEFAULT NULL,
 `logo_path` varchar(255) DEFAULT NULL,
 `is_locationlocked` tinyint(1) NOT NULL DEFAULT 0,
 `postal_address` text DEFAULT NULL,
 `phone_numbers` text DEFAULT NULL,
 `email_addresses` text DEFAULT NULL,
 `is_active` tinyint(1) NOT NULL DEFAULT 1,
 `license_status` varchar(50) DEFAULT NULL,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
dakoii_users	CREATE TABLE `dakoii_users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(255) NOT NULL,
 `username` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `orgcode` varchar(500) NOT NULL,
 `role` varchar(100) NOT NULL,
 `is_active` tinyint(1) DEFAULT 0,
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_by` int(11) unsigned DEFAULT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
education_levels	CREATE TABLE `education_levels` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(150) NOT NULL COMMENT 'Education level name',
 `remarks` text DEFAULT NULL COMMENT 'Additional notes or remarks',
 `priority` int(10) unsigned NOT NULL DEFAULT 0,
 `created_by` int(11) unsigned DEFAULT NULL COMMENT 'User ID who created the record',
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_by` int(11) unsigned DEFAULT NULL COMMENT 'User ID who last updated the record',
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
exercises	CREATE TABLE `exercises` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `exercise_name` varchar(255) NOT NULL,
 `gazzetted_no` varchar(255) NOT NULL,
 `gazzetted_date` date NOT NULL,
 `advertisement_no` varchar(255) NOT NULL,
 `advertisement_date` date NOT NULL,
 `is_internal` tinyint(1) NOT NULL COMMENT 'is internal advertisment or external advertisement',
 `mode_of_advertisement` varchar(100) NOT NULL,
 `publish_date_from` datetime NOT NULL,
 `publish_date_to` datetime NOT NULL,
 `description` text DEFAULT NULL,
 `applicants_information` text DEFAULT NULL COMMENT 'Information provided to applicants',
 `applicants_notice` text DEFAULT NULL COMMENT 'Notice to applicants',
 `pre_screen_criteria` text NOT NULL,
 `status` enum('published','draft','selection','archived') NOT NULL DEFAULT 'draft',
 `created_by` int(11) NOT NULL,
 `updated_by` int(11) NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_exercises_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores job advertisement exercises'
geo_countries	CREATE TABLE `geo_countries` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `name` varchar(100) NOT NULL,
 `country_code` char(2) NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `created_by` int(10) unsigned DEFAULT NULL,
 `updated_by` int(10) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uq_country_code` (`country_code`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
geo_districts	CREATE TABLE `geo_districts` (
 `id` int(11) NOT NULL AUTO_INCREMENT,
 `district_code` varchar(10) DEFAULT NULL,
 `name` varchar(100) NOT NULL,
 `country_id` int(10) unsigned NOT NULL,
 `province_id` int(10) unsigned NOT NULL,
 `json_id` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `created_by` int(10) unsigned DEFAULT NULL,
 `updated_by` int(10) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `idx_country_id` (`country_id`),
 KEY `idx_province_id` (`province_id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
geo_provinces	CREATE TABLE `geo_provinces` (
 `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
 `province_code` varchar(10) NOT NULL,
 `name` varchar(100) NOT NULL,
 `country_id` int(10) unsigned NOT NULL,
 `json_id` varchar(50) DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `created_by` int(10) unsigned DEFAULT NULL,
 `updated_by` int(10) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 UNIQUE KEY `uq_province_code` (`province_code`),
 KEY `idx_country_id` (`country_id`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
positions	CREATE TABLE `positions` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `exercise_id` int(11) unsigned DEFAULT NULL,
 `org_id` int(11) NOT NULL,
 `position_group_id` int(11) unsigned DEFAULT NULL,
 `position_reference` varchar(50) NOT NULL,
 `designation` varchar(255) NOT NULL,
 `classification` varchar(100) NOT NULL,
 `award` varchar(100) NOT NULL,
 `location` varchar(255) NOT NULL,
 `annual_salary` varchar(100) NOT NULL,
 `qualifications` text NOT NULL,
 `knowledge` text NOT NULL,
 `skills_competencies` text NOT NULL,
 `job_experiences` text NOT NULL,
 `jd_filepath` varchar(255) NOT NULL,
 `jd_texts_extracted` text NOT NULL,
 `remarks` text NOT NULL,
 `status` enum('active','withdrawn') NOT NULL DEFAULT 'active',
 `status_at` datetime DEFAULT NULL,
 `status_by` int(11) unsigned DEFAULT NULL,
 `status_remarks` text DEFAULT NULL,
 `created_by` int(11) NOT NULL,
 `updated_by` int(11) NOT NULL,
 `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
 `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stores available job positions'
positions_groups	CREATE TABLE `positions_groups` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `exercise_id` int(11) NOT NULL,
 `parent_id` int(11) DEFAULT NULL,
 `group_name` varchar(255) NOT NULL,
 `description` text DEFAULT NULL,
 `created_by` varchar(255) NOT NULL,
 `updated_by` varchar(255) NOT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
rate_items	CREATE TABLE `rate_items` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `item_label` varchar(150) NOT NULL COMMENT 'Label or title of the item',
 `description` text DEFAULT NULL,
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` int(11) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
rate_items_scores	CREATE TABLE `rate_items_scores` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `item_id` int(11) unsigned NOT NULL COMMENT 'ID of the item in adx_items',
 `score` int(11) NOT NULL COMMENT 'Score value for the item',
 `label` varchar(100) NOT NULL COMMENT 'label that will appear of the item\r\n',
 `score_description` varchar(255) DEFAULT NULL COMMENT 'Description or justification for the score',
 `created_at` datetime NOT NULL DEFAULT current_timestamp(),
 `created_by` int(11) unsigned DEFAULT NULL,
 `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
 `updated_by` int(11) unsigned DEFAULT NULL,
 `deleted_at` datetime DEFAULT NULL,
 `deleted_by` int(11) unsigned DEFAULT NULL,
 PRIMARY KEY (`id`),
 KEY `item_id` (`item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
users	CREATE TABLE `users` (
 `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
 `org_id` int(11) NOT NULL,
 `orgcode` int(11) NOT NULL,
 `fileno` varchar(100) NOT NULL,
 `name` varchar(255) NOT NULL,
 `username` varchar(255) NOT NULL,
 `password` varchar(255) NOT NULL,
 `role` enum('admin','supervisor','user','guest') NOT NULL DEFAULT 'user',
 `position` varchar(255) DEFAULT NULL,
 `id_photo` varchar(500) NOT NULL,
 `phone` varchar(200) NOT NULL,
 `email` varchar(500) NOT NULL,
 `status` varchar(20) NOT NULL,
 `created_by` varchar(200) DEFAULT NULL,
 `updated_by` varchar(200) DEFAULT NULL,
 `created_at` datetime DEFAULT current_timestamp(),
 `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
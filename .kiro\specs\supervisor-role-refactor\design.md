# Design Document

## Overview

This design outlines the refactoring of the supervisor role system in DERS from a role-based approach to a capability-based approach. The change involves adding an `is_supervisor` boolean field to the users table and updating all related code to use this field instead of checking for role='supervisor'.

## Architecture

### Database Schema Changes

The users table will be modified to include a new `is_supervisor` field:

```sql
ALTER TABLE users ADD COLUMN is_supervisor TINYINT(1) DEFAULT 0 AFTER role;
```

### Migration Strategy

1. **Add the new field**: Add `is_supervisor` column with default value 0
2. **Migrate existing data**: Update users with role='supervisor' to have `is_supervisor=1` and role='user'
3. **Update constraints**: Modify the role enum to exclude 'supervisor'
4. **Update code**: Replace all role-based supervisor checks with capability-based checks

## Components and Interfaces

### UsersModel Updates

The UsersModel will be updated with:

1. **Field Addition**: Add `is_supervisor` to `$allowedFields` array
2. **Validation Updates**: Remove 'supervisor' from role validation rules
3. **New Methods**:
   - `getSupervisors()` - Get all users with supervisor capability
   - `getUsersWithSupervisorCapability($orgId)` - Get supervisors by organization
   - `setSupervisorCapability($userId, $isSupervisor)` - Set supervisor capability for a user

### Controller Updates

Controllers that currently check for supervisor role will be updated:

1. **HomeAuthController**: Update role checking logic in authentication
2. **AdminController**: Update access control checks
3. **Dakoii**: Update user management and validation
4. **Auth Filter**: Update authorization logic

### View Updates

Views that display or manage supervisor information:

1. **User Management Views**: Add supervisor capability checkbox/toggle
2. **User Lists**: Display supervisor capability badge separately from role
3. **Forms**: Update validation messages and role options

## Data Models

### Users Table Schema (Updated)

```sql
CREATE TABLE `users` (
  `id` int(11) UNSIGNED NOT NULL,
  `org_id` int(11) NOT NULL,
  `orgcode` int(11) NOT NULL,
  `fileno` varchar(100) NOT NULL,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','user','guest') NOT NULL DEFAULT 'user',
  `is_supervisor` tinyint(1) DEFAULT 0,
  `position` varchar(255) DEFAULT NULL,
  `id_photo` varchar(500) NOT NULL,
  `phone` varchar(200) NOT NULL,
  `email` varchar(500) NOT NULL,
  `status` varchar(20) NOT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

### Session Data Structure

Session data will include supervisor capability:

```php
[
    'logged_in' => true,
    'user_id' => $user['id'],
    'name' => $user['name'],
    'username' => $user['username'],
    'role' => $user['role'],
    'is_supervisor' => $user['is_supervisor'],
    'org_id' => $user['org_id'],
    'orgcode' => $user['orgcode']
]
```

## Error Handling

### Migration Error Handling

1. **Backup Strategy**: Create database backup before migration
2. **Rollback Plan**: Provide rollback script if migration fails
3. **Validation**: Verify data integrity after migration

### Code Update Error Handling

1. **Gradual Deployment**: Update code in phases to minimize risk
2. **Fallback Logic**: Temporarily support both old and new approaches during transition
3. **Testing**: Comprehensive testing of all supervisor-related functionality

### User Interface Error Handling

1. **Form Validation**: Clear error messages for role validation
2. **Capability Management**: Proper error handling when setting supervisor capabilities
3. **Display Logic**: Graceful handling of missing supervisor data

## Testing Strategy

### Database Testing

1. **Migration Testing**: Test migration script on copy of production data
2. **Data Integrity**: Verify all supervisor users are correctly migrated
3. **Constraint Testing**: Ensure role enum constraints work correctly

### Unit Testing

1. **Model Testing**: Test new UsersModel methods
2. **Validation Testing**: Test updated validation rules
3. **Authentication Testing**: Test supervisor capability checks

### Integration Testing

1. **Controller Testing**: Test all updated controllers
2. **View Testing**: Test user interface changes
3. **Session Testing**: Test session data handling

### User Acceptance Testing

1. **Admin Workflow**: Test user management with supervisor capabilities
2. **Authentication Flow**: Test login and access control
3. **Display Testing**: Verify correct display of supervisor status

## Implementation Phases

### Phase 1: Database Migration
- Create migration script
- Add `is_supervisor` field
- Migrate existing supervisor role data
- Update database constraints

### Phase 2: Model Updates
- Update UsersModel
- Add new methods for supervisor capability
- Update validation rules

### Phase 3: Controller Updates
- Update authentication logic
- Update access control checks
- Update user management functionality

### Phase 4: View Updates
- Update user management forms
- Update user display views
- Update validation messages

### Phase 5: Testing and Deployment
- Comprehensive testing
- Documentation updates
- Production deployment
-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 28, 2025 at 08:10 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ders_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `appx_application_details`
--

CREATE TABLE `appx_application_details` (
  `id` int(11) NOT NULL,
  `org_id` int(11) NOT NULL,
  `exercise_id` int(11) NOT NULL,
  `applicant_id` int(11) NOT NULL,
  `position_id` int(11) NOT NULL,
  `application_number` varchar(20) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `place_of_origin` varchar(255) DEFAULT NULL,
  `id_photo_path` varchar(255) DEFAULT NULL,
  `contact_details` varchar(500) DEFAULT NULL,
  `location_address` varchar(255) DEFAULT NULL,
  `id_numbers` varchar(255) DEFAULT NULL,
  `current_employer` varchar(255) DEFAULT NULL,
  `current_position` varchar(255) DEFAULT NULL,
  `current_salary` decimal(10,2) DEFAULT NULL,
  `citizenship` varchar(50) DEFAULT NULL,
  `marital_status` varchar(20) DEFAULT NULL,
  `date_of_marriage` date DEFAULT NULL,
  `spouse_employer` varchar(255) DEFAULT NULL,
  `is_public_servant` tinyint(1) NOT NULL DEFAULT 0,
  `public_service_file_number` varchar(20) DEFAULT NULL,
  `employee_of_org_id` int(11) UNSIGNED DEFAULT NULL,
  `children` text DEFAULT NULL,
  `offence_convicted` text DEFAULT NULL,
  `referees` text DEFAULT NULL,
  `how_did_you_hear_about_us` varchar(255) DEFAULT NULL,
  `signature_path` varchar(255) DEFAULT NULL,
  `publications` text DEFAULT NULL,
  `awards` text DEFAULT NULL,
  `is_received` tinyint(1) NOT NULL DEFAULT 0,
  `files_extracted_texts` text NOT NULL,
  `received_status` varchar(11) NOT NULL,
  `received_by` int(11) UNSIGNED NOT NULL,
  `received_at` datetime NOT NULL,
  `application_status` varchar(50) DEFAULT NULL,
  `remarks` text NOT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `pre_screened_at` datetime DEFAULT NULL,
  `pre_screened_by` int(11) UNSIGNED DEFAULT NULL,
  `pre_screened_status` varchar(50) DEFAULT NULL,
  `pre_screened_remarks` text DEFAULT NULL,
  `pre_screened_criteria_results` longtext DEFAULT NULL,
  `profile_status` varchar(20) NOT NULL,
  `profile_details` longtext NOT NULL,
  `profiled_by` int(11) UNSIGNED DEFAULT NULL,
  `profiled_at` datetime DEFAULT NULL,
  `rating_age` int(5) DEFAULT NULL,
  `rating_age_max` int(5) DEFAULT NULL,
  `rating_qualification` int(5) DEFAULT NULL,
  `rating_qualification_max` int(5) DEFAULT NULL,
  `rating_experience_private_non_relevant` int(5) DEFAULT NULL,
  `rating_experience_private_non_relevant_max` int(5) DEFAULT NULL,
  `rating_experience_private_relevant` int(5) DEFAULT NULL,
  `rating_experience_private_relevant_max` int(5) DEFAULT NULL,
  `rating_experience_public_non_relevant` int(5) DEFAULT NULL,
  `rating_experience_public_non_relevant_max` int(5) DEFAULT NULL,
  `rating_experience_public_relevant` int(5) DEFAULT NULL,
  `rating_experience_public_relevant_max` int(5) DEFAULT NULL,
  `rating_trainings` int(5) DEFAULT NULL,
  `rating_trainings_max` int(5) DEFAULT NULL,
  `rating_skills_competencies` int(5) DEFAULT NULL,
  `rating_skills_competencies_max` int(5) DEFAULT NULL,
  `rating_knowledge` int(5) DEFAULT NULL,
  `rating_knowledge_max` int(5) DEFAULT NULL,
  `rating_public_service` int(5) DEFAULT NULL,
  `rating_public_service_max` int(5) DEFAULT NULL,
  `rating_capability` int(5) DEFAULT NULL,
  `rating_capability_max` int(5) DEFAULT NULL,
  `rating_remarks` text DEFAULT NULL,
  `rating_status` varchar(11) DEFAULT NULL,
  `rated_by` int(11) UNSIGNED DEFAULT NULL,
  `rated_at` datetime DEFAULT NULL,
  `shortlist_status` varchar(11) DEFAULT NULL,
  `shortlisted_by` int(11) UNSIGNED DEFAULT NULL,
  `shortlisted_at` datetime DEFAULT NULL,
  `interviewed` varchar(11) DEFAULT NULL,
  `interviewed_by` int(11) UNSIGNED DEFAULT NULL,
  `interviewed_at` datetime DEFAULT NULL,
  `interview_rated` varchar(11) DEFAULT NULL,
  `pre_selection_status` varchar(11) DEFAULT NULL,
  `pre_selection_by` int(11) UNSIGNED DEFAULT NULL,
  `preselection_at` datetime DEFAULT NULL,
  `selected_status` varchar(20) DEFAULT NULL,
  `selected_by` int(11) UNSIGNED DEFAULT NULL,
  `selected_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `deleted_by` int(11) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `appx_application_details`
--

INSERT INTO `appx_application_details` (`id`, `org_id`, `exercise_id`, `applicant_id`, `position_id`, `application_number`, `first_name`, `last_name`, `gender`, `date_of_birth`, `place_of_origin`, `id_photo_path`, `contact_details`, `location_address`, `id_numbers`, `current_employer`, `current_position`, `current_salary`, `citizenship`, `marital_status`, `date_of_marriage`, `spouse_employer`, `is_public_servant`, `public_service_file_number`, `employee_of_org_id`, `children`, `offence_convicted`, `referees`, `how_did_you_hear_about_us`, `signature_path`, `publications`, `awards`, `is_received`, `files_extracted_texts`, `received_status`, `received_by`, `received_at`, `application_status`, `remarks`, `created_by`, `updated_by`, `created_at`, `updated_at`, `pre_screened_at`, `pre_screened_by`, `pre_screened_status`, `pre_screened_remarks`, `pre_screened_criteria_results`, `profile_status`, `profile_details`, `profiled_by`, `profiled_at`, `rating_age`, `rating_age_max`, `rating_qualification`, `rating_qualification_max`, `rating_experience_private_non_relevant`, `rating_experience_private_non_relevant_max`, `rating_experience_private_relevant`, `rating_experience_private_relevant_max`, `rating_experience_public_non_relevant`, `rating_experience_public_non_relevant_max`, `rating_experience_public_relevant`, `rating_experience_public_relevant_max`, `rating_trainings`, `rating_trainings_max`, `rating_skills_competencies`, `rating_skills_competencies_max`, `rating_knowledge`, `rating_knowledge_max`, `rating_public_service`, `rating_public_service_max`, `rating_capability`, `rating_capability_max`, `rating_remarks`, `rating_status`, `rated_by`, `rated_at`, `shortlist_status`, `shortlisted_by`, `shortlisted_at`, `interviewed`, `interviewed_by`, `interviewed_at`, `interview_rated`, `pre_selection_status`, `pre_selection_by`, `preselection_at`, `selected_status`, `selected_by`, `selected_at`, `deleted_at`, `deleted_by`) VALUES
(2, 0, 0, 1, 11, 'APP202582766', 'Aitapes', 'ITUs', 'Male', '2003-02-04', 'EHP, Koookol', 'uploads/photos/1_1743287586_1743287586_bc7a2b9738a11b413161.jpg', '44455567', '', 'NID: 2314342, ', 'Curr Emplee', 'Curr Poseee', 344000.00, 'PNGs', 'married', '2013-01-28', 'BSP', 0, NULL, NULL, '[{\"name\":\"Cool Mix\",\"dob\":\"2012-02-04\",\"gender\":\"Male\"},{\"name\":\"Rimrim\",\"dob\":\"2025-02-11\",\"gender\":\"\"}]', '', 'Referees List', 'other', NULL, 'Pub list', 'Award list', 0, '', '2025-03-19 ', 14, '2025-03-19 12:10:14', 'active', '', 1, 14, '2025-03-17 10:44:31', '2025-04-27 18:49:43', '2025-04-27 01:27:41', 14, 'passed', 'Thisfisdfs', '[{\"criteriaIndex\":\"0\",\"met\":false,\"remarks\":\"Certificate looks fake\"},{\"criteriaIndex\":\"1\",\"met\":true,\"remarks\":\"\"},{\"criteriaIndex\":\"2\",\"met\":true,\"remarks\":\"\"}]', 'completed', '{\n    \"ai_generated_profile\": \"**I. Core Identifiers & Contact Information:**\\n\\n*   **Name:** Aitapes ITUs\\n*   **Date of Birth and Age:** 2003-02-04, 22 years old\\n*   **Sex:** Male\\n*   **Address\\/Location:** Information not available\\n*   **Contact Details:** 44455567\\n*   **Place of Origin:** EHP, Koookol\\n*   **NID Number:** NID: 2314342\\n\\n**II. Employment Information:**\\n\\n*   **Current Position:** Curr Poseee\\n*   **Current Employer:** Curr Emplee\\n*   **Public Service Status:** Status not confirmed\\n\\n**III. Education & Training:**\\n\\n*   **Qualifications:**\\n    *   Doctorate in dasdasd, Cook University (2024-01-27 to 2024-02-08)\\n    *   Diploma in fdsfd, Cool Institute (2025-01-27 to 2025-02-11)\\n*   **Other Training\\/Courses Attended:** Information not available\\n\\n**IV. Knowledge, Skills, and Competencies:**\\n\\n*   **Knowledge:**\\n    *   Expertise in dasdasd (Doctorate, Cook University)\\n    *   Knowledge of fdsfd (Diploma, Cool Institute)\\n    *   Understanding gained from experience as Rite at Curr Emplee, including Work Descript.\\n*   **Skills\\/Competencies:**\\n    *   Skills related to Work Descript, gained from work description at Curr Emplee.\\n    *   Skills related to Achievedved, gained from achievements at Curr Emplee.\\n\\n**V. Experience:**\\n\\n*   **Related Job Experience:**\\n    *   Rite, Curr Emplee (2024-02-04 to -0001-11-30). Responsibilities included Work Descript. Key achievement: Achievedved.\\n\\n**VI. Performance & Achievements:**\\n\\n*   **Performance Level:** Not available in application data\\n*   **Publications:** Information not available\\n*   **Awards:** Award list\\n\\n**VII. Verification (Supporting Information):**\\n\\n*   **Referees:** Referees List\\n\",\n    \"profile_data\": {\n        \"core_identifiers\": {\n            \"name\": \"Aitapes ITUs\",\n            \"date_of_birth\": \"2003-02-04\",\n            \"age\": 22,\n            \"sex\": \"Male\",\n            \"address_location\": \"\",\n            \"contact_details\": \"44455567\",\n            \"place_of_origin\": \"EHP, Koookol\",\n            \"nid_number\": \"NID: 2314342, \",\n            \"citizenship\": \"PNGs\",\n            \"spouse_employer\": \"BSP\",\n            \"children\": \"[{\\\"name\\\":\\\"Cool Mix\\\",\\\"dob\\\":\\\"2012-02-04\\\",\\\"gender\\\":\\\"Male\\\"},{\\\"name\\\":\\\"Rimrim\\\",\\\"dob\\\":\\\"2025-02-11\\\",\\\"gender\\\":\\\"\\\"}]\"\n        },\n        \"employment_information\": {\n            \"current_position\": \"Curr Poseee\",\n            \"current_employer\": \"Curr Emplee\",\n            \"current_salary\": \"344000.00\",\n            \"public_service_status\": \"Status not confirmed\"\n        },\n        \"education_training\": {\n            \"qualifications\": [\n                {\n                    \"level\": \"Doctorate\",\n                    \"institution\": \"Cook University\",\n                    \"course\": \"dasdasd\",\n                    \"units\": \"dasdasd\\r\\nTRTYRTYRT\",\n                    \"date_from\": \"2024-01-27\",\n                    \"date_to\": \"2024-02-08\"\n                },\n                {\n                    \"level\": \"Diploma\",\n                    \"institution\": \"Cool Institute\",\n                    \"course\": \"fdsfd\",\n                    \"units\": \"dfsd\",\n                    \"date_from\": \"2025-01-27\",\n                    \"date_to\": \"2025-02-11\"\n                }\n            ],\n            \"position_applied_for\": {\n                \"title\": \"Forest\",\n                \"requirements\": \"Not specified\"\n            },\n            \"training_courses\": []\n        },\n        \"knowledge_skills\": {\n            \"knowledge\": [\n                {\n                    \"source\": \"Doctorate in dasdasd at Cook University\",\n                    \"field\": \"dasdasd\"\n                },\n                {\n                    \"source\": \"Diploma in fdsfd at Cool Institute\",\n                    \"field\": \"fdsfd\"\n                },\n                {\n                    \"source\": \"Experience as Rite  at Curr Emplee\",\n                    \"details\": \"Work Descript\"\n                }\n            ],\n            \"skills_competencies\": [\n                {\n                    \"source\": \"Work description at Curr Emplee\",\n                    \"details\": \"Work Descript\"\n                },\n                {\n                    \"source\": \"Achievements at Curr Emplee\",\n                    \"details\": \"Achiedved\"\n                }\n            ]\n        },\n        \"experience\": {\n            \"related_job_experience\": [\n                {\n                    \"position\": \"Rite \",\n                    \"employer\": \"Curr Emplee\",\n                    \"employer_contacts_address\": \"Employee Addres Contacts\",\n                    \"date_from\": \"2024-02-04\",\n                    \"date_to\": \"-0001-11-30\",\n                    \"work_description\": \"Work Descript\",\n                    \"achievements\": \"Achiedved\",\n                    \"public_service\": \"No\"\n                }\n            ],\n            \"experience_summary\": {\n                \"total_years\": \"1.2\",\n                \"total_positions\": 1,\n                \"earliest_experience\": \"2024-02-04\"\n            }\n        },\n        \"performance_achievements\": {\n            \"performance_level\": \"Not available in application data\",\n            \"publications\": [],\n            \"awards\": [\n                \"Award list\"\n            ],\n            \"achievements\": [\n                {\n                    \"role\": \"Rite \",\n                    \"employer\": \"Curr Emplee\",\n                    \"details\": \"Achiedved\"\n                }\n            ]\n        },\n        \"verification\": {\n            \"referees\": \"Referees List\"\n        },\n        \"application_details\": {\n            \"application_number\": \"APP202582766\",\n            \"application_date\": \"2025-03-17\",\n            \"position_group\": \"Frefes\",\n            \"exercise\": \"2nd Job Advertisement\",\n            \"marital_status\": \"married\",\n            \"how_did_you_hear_about_us\": \"other\"\n        }\n    }\n}', 14, '2025-04-27 04:39:19', 4, 8, 7, 10, 2, 4, 3, 5, 1, 5, 3, 5, 2, 5, 3, 5, 4, 5, 3, 3, 3, 5, NULL, 'completed', 14, '2025-04-27 18:49:43', '', 0, NULL, '', 0, NULL, '', '', 0, NULL, '', 0, NULL, NULL, NULL),
(4, 0, 0, 2, 9, 'APP202557768', 'Anzii', 'Ori Nols', 'Male', '2002-03-05', 'ESP, KOKOI', 'uploads/photos/2_1742172899_1742172899_956cb039513ac76db166.png', '2534645', 'My Addresseee', 'NID 23224234', 'Curr Emplee', 'Head Teacher', 344000.00, 'PNGAusee', 'single', '0000-00-00', '', 0, NULL, NULL, '[{\"name\":\"Wan liklik boit\",\"dob\":\"2025-02-25\",\"gender\":\"Male\"}]', '', 'Ref one:\r\nRef two \r\nRef teee', 'other', NULL, '', '', 0, '', '2025-03-19 ', 14, '2025-03-19 12:12:32', 'active', '', 2, 14, '2025-03-17 11:56:35', '2025-04-26 15:29:40', '2025-04-26 15:29:40', 14, 'passed', 'asdjao', '', '', '', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', 0, NULL, '', 0, NULL, '', 0, NULL, '', '', 0, NULL, '', 0, NULL, NULL, NULL),
(5, 0, 0, 3, 6, 'APP202518319', 'Testa', 'Dakoii', 'Male', '2001-04-02', 'Cokokcoko', NULL, '73183178', 'Wewak Hill', 'NID = 71112232323', 'NIL', 'NIL', 0.00, 'PNG', 'married', '2025-04-08', 'BSP', 0, NULL, NULL, '[{\"name\":\"Cool Mixfix\",\"dob\":\"2025-04-03\",\"gender\":\"Male\"},{\"name\":\"Rimrim\",\"dob\":\"2023-04-22\",\"gender\":\"Female\"}]', '', 'Referere', 'newspaper', NULL, 'Pub one aodn', 'Liste s alda', 0, '', '2025-04-26 ', 14, '2025-04-26 15:25:37', 'active', '', 3, 14, '2025-04-26 15:25:11', '2025-04-27 06:26:17', '2025-04-26 23:42:09', 14, 'passed', 'Fsdfs', '[{\"criteriaIndex\":\"0\",\"met\":true,\"remarks\":\"\"},{\"criteriaIndex\":\"1\",\"met\":true,\"remarks\":\"\"},{\"criteriaIndex\":\"2\",\"met\":true,\"remarks\":\"This is the remarks\"}]', 'completed', '{\n    \"ai_generated_profile\": \"**I. Core Identifiers & Contact Information:**\\n\\n*   **Name:** Testa Dakoii\\n*   **Date of Birth and Age:** 2001-04-02, Age: 24\\n*   **Sex:** Male\\n*   **Address\\/Location:** Wewak Hill\\n*   **Contact Details:** 73183178\\n*   **Place of Origin:** Cokokcoko\\n*   **NID Number:** NID = 71112232323\\n\\n**II. Employment Information:**\\n\\n*   **Current Position:** NIL\\n*   **Current Employer:** NIL\\n*   **Public Service Status:** Status not confirmed\\n\\n**III. Education & Training:**\\n\\n*   **Qualifications:** Cooking (Unknown level) from DWU dasdk (2001-04-01 to 2004-04-17). Units: FSDfsd DSKFNSLDKN\\n*   **Other Training\\/Courses Attended:** Information not available\\n\\n**IV. Knowledge, Skills, and Competencies:**\\n\\n*   **Knowledge:** Cooking at DWU dasdk; Experience as Pfodf at SDFSFSD (details: FDFsdjp)\\n*   **Skills\\/Competencies:** FDFsdjp (based on work description at SDFSFSD); fnksdnfls (based on achievements at SDFSFSD)\\n\\n**V. Experience:**\\n\\n*   **Related Job Experience:** Pfodf at SDFSFSD (2001-04-08 to 2004-04-26). Address: fsdfsd. Work Description: FDFsdjp. Achievements: fnksdnfls. Public Service: No.\\n*   **Experience Summary:** Total years: 3.0, Total positions: 1, Earliest experience: 2001-04-08\\n\\n**VI. Performance & Achievements:**\\n\\n*   **Performance Level:** Not available in application data\\n*   **Publications:** None\\n*   **Awards:** Liste s alda\\n*   **Achievements:** fnksdnfls as Pfodf at SDFSFSD\\n\\n**VII. Verification (Supporting Information):**\\n\\n*   **Referees:** Referere\\n\",\n    \"profile_data\": {\n        \"core_identifiers\": {\n            \"name\": \"Testa Dakoii\",\n            \"date_of_birth\": \"2001-04-02\",\n            \"age\": 24,\n            \"sex\": \"Male\",\n            \"address_location\": \"Wewak Hill\",\n            \"contact_details\": \"73183178\",\n            \"place_of_origin\": \"Cokokcoko\",\n            \"nid_number\": \"NID = 71112232323\",\n            \"citizenship\": \"PNG\",\n            \"spouse_employer\": \"BSP\",\n            \"children\": \"[{\\\"name\\\":\\\"Cool Mixfix\\\",\\\"dob\\\":\\\"2025-04-03\\\",\\\"gender\\\":\\\"Male\\\"},{\\\"name\\\":\\\"Rimrim\\\",\\\"dob\\\":\\\"2023-04-22\\\",\\\"gender\\\":\\\"Female\\\"}]\"\n        },\n        \"employment_information\": {\n            \"current_position\": \"NIL\",\n            \"current_employer\": \"NIL\",\n            \"current_salary\": \"0.00\",\n            \"public_service_status\": \"Status not confirmed\"\n        },\n        \"education_training\": {\n            \"qualifications\": [\n                {\n                    \"level\": \"Unknown\",\n                    \"institution\": \"DWU dasdk\",\n                    \"course\": \"Cooking\",\n                    \"units\": \"FSDfsd\\r\\nDSKFNSLDKN\",\n                    \"date_from\": \"2001-04-01\",\n                    \"date_to\": \"2004-04-17\"\n                }\n            ],\n            \"position_applied_for\": {\n                \"title\": \"Post Number\",\n                \"requirements\": \"Not specified\"\n            },\n            \"training_courses\": []\n        },\n        \"knowledge_skills\": {\n            \"knowledge\": [\n                {\n                    \"source\": \"Unknown in Cooking at DWU dasdk\",\n                    \"field\": \"Cooking\"\n                },\n                {\n                    \"source\": \"Experience as Pfodf at SDFSFSD\",\n                    \"details\": \"FDFsdjp\"\n                }\n            ],\n            \"skills_competencies\": [\n                {\n                    \"source\": \"Work description at SDFSFSD\",\n                    \"details\": \"FDFsdjp\"\n                },\n                {\n                    \"source\": \"Achievements at SDFSFSD\",\n                    \"details\": \"fnksdnfls\"\n                }\n            ]\n        },\n        \"experience\": {\n            \"related_job_experience\": [\n                {\n                    \"position\": \"Pfodf\",\n                    \"employer\": \"SDFSFSD\",\n                    \"employer_contacts_address\": \"fsdfsd\",\n                    \"date_from\": \"2001-04-08\",\n                    \"date_to\": \"2004-04-26\",\n                    \"work_description\": \"FDFsdjp\",\n                    \"achievements\": \"fnksdnfls\",\n                    \"public_service\": \"No\"\n                }\n            ],\n            \"experience_summary\": {\n                \"total_years\": \"3.0\",\n                \"total_positions\": 1,\n                \"earliest_experience\": \"2001-04-08\"\n            }\n        },\n        \"performance_achievements\": {\n            \"performance_level\": \"Not available in application data\",\n            \"publications\": [],\n            \"awards\": [\n                \"Liste s alda\"\n            ],\n            \"achievements\": [\n                {\n                    \"role\": \"Pfodf\",\n                    \"employer\": \"SDFSFSD\",\n                    \"details\": \"fnksdnfls\"\n                }\n            ]\n        },\n        \"verification\": {\n            \"referees\": \"Referere\"\n        },\n        \"application_details\": {\n            \"application_number\": \"APP202518319\",\n            \"application_date\": \"2025-04-26\",\n            \"position_group\": \"Frefes\",\n            \"exercise\": \"2nd Job Advertisement\",\n            \"marital_status\": \"married\",\n            \"how_did_you_hear_about_us\": \"newspaper\"\n        }\n    }\n}', 14, '2025-04-27 06:26:17', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', 0, NULL, '', 0, NULL, '', 0, NULL, '', '', 0, NULL, '', 0, NULL, NULL, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `appx_application_details`
--
ALTER TABLE `appx_application_details`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_pre_screened` (`pre_screened_at`),
  ADD KEY `idx_pre_screened_status` (`pre_screened_status`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `appx_application_details`
--
ALTER TABLE `appx_application_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

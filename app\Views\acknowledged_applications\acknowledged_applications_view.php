<?php
/**
 * View file for displaying individual acknowledged application details
 *
 * @var array $application Application details
 * @var array $files Application files
 * @var array $experiences Application experiences
 * @var array $education Application education
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-check-circle me-2"></i>Acknowledged Application Details</h2>
            <p class="text-muted">Application #<?= $application['application_number'] ?></p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?= base_url('acknowledged_applications') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Acknowledged Applications
            </a>
        </div>
    </div>

    <!-- Application Status Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Application Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Status:</strong><br>
                            <span class="badge bg-success fs-6">Acknowledged</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Date Applied:</strong><br>
                            <?= date('d M Y, H:i', strtotime($application['created_at'])) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Date Acknowledged:</strong><br>
                            <?= date('d M Y, H:i', strtotime($application['received_at'])) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Application Status:</strong><br>
                            <?= ucfirst($application['application_status'] ?? 'Active') ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Position Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Position Title:</strong> <?= esc($application['position_title']) ?></p>
                            <p><strong>Position Reference:</strong> <?= esc($application['position_reference']) ?></p>
                            <p><strong>Classification:</strong> <?= esc($application['classification']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Organization:</strong> <?= esc($application['org_name']) ?></p>
                            <p><strong>Exercise:</strong> <?= esc($application['exercise_name']) ?></p>
                            <p><strong>Advertisement No:</strong> <?= esc($application['advertisement_no']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applicant Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Applicant Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Full Name:</strong> <?= esc($application['first_name'] . ' ' . $application['last_name']) ?></p>
                            <p><strong>Gender:</strong> <?= esc($application['gender']) ?></p>
                            <p><strong>Date of Birth:</strong> <?= $application['date_of_birth'] ? date('d M Y', strtotime($application['date_of_birth'])) : 'N/A' ?></p>
                            <p><strong>Place of Origin:</strong> <?= esc($application['place_of_origin']) ?></p>
                            <p><strong>Citizenship:</strong> <?= esc($application['citizenship']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Current Employer:</strong> <?= esc($application['current_employer']) ?></p>
                            <p><strong>Current Position:</strong> <?= esc($application['current_position']) ?></p>
                            <p><strong>Current Salary:</strong> <?= $application['current_salary'] ? 'K' . number_format($application['current_salary'], 2) : 'N/A' ?></p>
                            <p><strong>Public Servant:</strong> <?= $application['is_public_servant'] ? 'Yes' : 'No' ?></p>
                            <?php if ($application['is_public_servant'] && !empty($application['public_service_file_number'])): ?>
                                <p><strong>File Number:</strong> <?= esc($application['public_service_file_number']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($application['contact_details']) && is_array($application['contact_details'])): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <?php if (!empty($application['contact_details']['email'])): ?>
                                    <p><strong>Email:</strong> <?= esc($application['contact_details']['email']) ?></p>
                                <?php endif; ?>
                                <?php if (!empty($application['contact_details']['mobile'])): ?>
                                    <p><strong>Mobile:</strong> <?= esc($application['contact_details']['mobile']) ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <?php if (!empty($application['contact_details']['phone'])): ?>
                                    <p><strong>Phone:</strong> <?= esc($application['contact_details']['phone']) ?></p>
                                <?php endif; ?>
                                <?php if (!empty($application['contact_details']['fax'])): ?>
                                    <p><strong>Fax:</strong> <?= esc($application['contact_details']['fax']) ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No contact details available</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Files -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Application Files</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($files)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>File Title</th>
                                        <th>File Type</th>
                                        <th>Upload Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($files as $file): ?>
                                        <tr>
                                            <td><?= esc($file['file_title'] ?? 'N/A') ?></td>
                                            <td><?= esc($file['file_type'] ?? 'Document') ?></td>
                                            <td><?= date('d M Y', strtotime($file['created_at'])) ?></td>
                                            <td>
                                                <?php if (!empty($file['file_path']) && file_exists(ROOTPATH . $file['file_path'])): ?>
                                                    <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-download"></i> View
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">File not available</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted">No files uploaded</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Remarks Section -->
    <?php if (!empty($application['remarks'])): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-comment me-2"></i>Remarks</h5>
                    </div>
                    <div class="card-body">
                        <p><?= nl2br(esc($application['remarks'])) ?></p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>
